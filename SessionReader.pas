program SessionReader;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils, Classes, fpjson, jsonparser, Math, LazUTF8, SessionUtils, Process;

type
  TRovoDevConfig = record
    ConfigFile: string;
    SessionsDir: string;
    IsValid: Boolean;
  end;

var
  Question: string;
  Response: string;
  Config: TRovoDevConfig;

procedure LogToFile(const message: string);
var
  LogFile: TextFile;
  TimeStamp: string;
begin
  try
    TimeStamp := FormatDateTime('yyyy-mm-dd hh:nn:ss', Now);
    AssignFile(LogFile, 'session_reader.log');
    if FileExists('session_reader.log') then
      Append(LogFile)
    else
      Rewrite(LogFile);

    WriteLn(LogFile, '[' + TimeStamp + '] ' + message);
    CloseFile(LogFile);
  except
    // 忽略日志错误
  end;
end;

function LoadRovoDevConfig(const configFile: string): TRovoDevConfig;
var
  ConfigLines: TStringList;
  i: Integer;
  Line, Key, Value: string;
  ColonPos: Integer;
begin
  Result.ConfigFile := configFile;
  Result.SessionsDir := '';
  Result.IsValid := False;

  if not FileExists(configFile) then
  begin
    WriteLn('配置文件不存在: ', configFile);
    Exit;
  end;

  ConfigLines := TStringList.Create;
  try
    ConfigLines.LoadFromFile(configFile);

    for i := 0 to ConfigLines.Count - 1 do
    begin
      Line := Trim(ConfigLines[i]);
      if (Line = '') or (Line[1] = '#') then
        Continue;

      ColonPos := Pos(':', Line);
      if ColonPos > 0 then
      begin
        Key := Trim(Copy(Line, 1, ColonPos - 1));
        Value := Trim(Copy(Line, ColonPos + 1, Length(Line)));

        if Key = 'persistenceDir' then
        begin
          Result.SessionsDir := Value;
          Result.IsValid := True;
        end;
      end;
    end;

    // 如果没有找到 persistenceDir，使用默认值
    if Result.SessionsDir = '' then
    begin
      if Pos('custom', configFile) > 0 then
        Result.SessionsDir := 'custom_sessions'
      else
        Result.SessionsDir := 'ai_dev_sessions';
      Result.IsValid := True;
    end;

    WriteLn('配置加载成功:');
    WriteLn('  配置文件: ', Result.ConfigFile);
    WriteLn('  会话目录: ', Result.SessionsDir);

  finally
    ConfigLines.Free;
  end;
end;

function CountSessionDirs(const baseDir: string): Integer;
var
  SearchResult: TSearchRec;
begin
  Result := 0;

  if not DirectoryExists(baseDir) then
    Exit;

  if FindFirst(baseDir + '\*', faDirectory, SearchResult) = 0 then
  begin
    repeat
      if (SearchResult.Attr and faDirectory) <> 0 then
      begin
        if (SearchResult.Name <> '.') and (SearchResult.Name <> '..') then
        begin
          if FileExists(baseDir + '\' + SearchResult.Name + '\session_context.json') then
            Inc(Result);
        end;
      end;
    until FindNext(SearchResult) <> 0;
    FindClose(SearchResult);
  end;
end;

function FindLatestSessionDir(const sessionsDir: string): string;
var
  SearchRec: TSearchRec;
  LatestTime: TDateTime;
  LatestDir: string;
  CurrentTime: TDateTime;
begin
  Result := '';
  LatestTime := 0;
  
  if FindFirst(sessionsDir + '\*', faDirectory, SearchRec) = 0 then
  begin
    repeat
      if (SearchRec.Name <> '.') and (SearchRec.Name <> '..') then
      begin
        CurrentTime := FileDateToDateTime(SearchRec.Time);
        if CurrentTime > LatestTime then
        begin
          LatestTime := CurrentTime;
          LatestDir := SearchRec.Name;
        end;
      end;
    until FindNext(SearchRec) <> 0;
    FindClose(SearchRec);
  end;
  
  if LatestDir <> '' then
    Result := sessionsDir + '\' + LatestDir + '\session_context.json';
end;

procedure LogSessionDetails(const sessionFile: string);
var
  JsonText: string;
  JsonData: TJSONData;
  JsonObject: TJSONObject;
  MessageHistory: TJSONArray;
  Message: TJSONObject;
  Parts: TJSONArray;
  Part: TJSONObject;
  i, j: Integer;
  MessageKind, Content, PartKind: string;
begin
  try
    LogToFile('=== 开始记录会话详细内容 ===');
    LogToFile('会话文件: ' + sessionFile);

    // 读取 JSON 文件
    with TStringList.Create do
    try
      LoadFromFile(sessionFile);
      JsonText := Text;
    finally
      Free;
    end;

    // 解析 JSON
    JsonData := GetJSON(JsonText);
    try
      JsonObject := JsonData as TJSONObject;
      MessageHistory := JsonObject.FindPath('message_history') as TJSONArray;

      LogToFile('消息历史总数: ' + IntToStr(MessageHistory.Count));
      LogToFile('');

      // 记录最近的几条消息
      for i := Max(0, MessageHistory.Count - 5) to MessageHistory.Count - 1 do
      begin
        Message := MessageHistory[i] as TJSONObject;
        MessageKind := Message.Get('kind');

        LogToFile('--- 消息 ' + IntToStr(i + 1) + ' ---');
        LogToFile('类型: ' + MessageKind);

        Parts := Message.FindPath('parts') as TJSONArray;
        if Assigned(Parts) then
        begin
          LogToFile('部分数量: ' + IntToStr(Parts.Count));

          for j := 0 to Parts.Count - 1 do
          begin
            Part := Parts[j] as TJSONObject;
            PartKind := Part.Get('part_kind');
            Content := Part.Get('content');

            LogToFile('  部分 ' + IntToStr(j + 1) + ':');
            LogToFile('    类型: ' + PartKind);
            if Content <> '' then
            begin
              LogToFile('    内容: ' + Copy(Content, 1, 200));
              if Length(Content) > 200 then
                LogToFile('    ... (内容被截断)');
            end;
          end;
        end;
        LogToFile('');
      end;

    finally
      JsonData.Free;
    end;

    LogToFile('=== 会话详细内容记录完成 ===');

  except
    on E: Exception do
    begin
      LogToFile('记录会话详情时出错: ' + E.Message);
      WriteLn('Error logging session details: ', E.Message);
    end;
  end;
end;

function DecodeUnicodeEscapes(const input: string): string;
var
  i: Integer;
  unicodeStr: string;
  unicodeValue: Cardinal;
  utf8Char: string;
begin
  Result := input;
  WriteLn('=== Unicode 解码过程 ===');
  WriteLn('原始输入: ', input);
  WriteLn('输入长度: ', Length(input), ' 字符');
  WriteLn('');

  i := 1;
  while i <= Length(Result) - 5 do
  begin
    if (Result[i] = '\') and (Result[i+1] = 'u') then
    begin
      unicodeStr := Copy(Result, i+2, 4);
      try
        unicodeValue := StrToInt('$' + unicodeStr);
        utf8Char := UnicodeToUTF8(unicodeValue);

        WriteLn('发现: \u', unicodeStr, ' -> Unicode值: ', unicodeValue, ' -> UTF8: "', utf8Char, '" (', Length(utf8Char), ' 字节)');

        Delete(Result, i, 6);
        Insert(utf8Char, Result, i);
        Inc(i, Length(utf8Char));
      except
        on E: Exception do
        begin
          WriteLn('转换失败: \u', unicodeStr, ' - 错误: ', E.Message);
          Inc(i);
        end;
      end;
    end
    else
      Inc(i);
  end;

  WriteLn('');
  WriteLn('解码结果: ', Result);
  WriteLn('结果长度: ', Length(Result), ' 字符');
  WriteLn('=== 解码完成 ===');
  WriteLn('');
end;

function ExtractLatestResponseFromRawJSON(const sessionFile: string): string;
var
  JsonText: string;
  i: Integer;
  ContentStart, ContentEnd: Integer;
  RawContent: string;
begin
  Result := '';

  try
    WriteLn('直接从原始 JSON 提取最新回复...');

    // 读取原始 JSON 文件
    with TStringList.Create do
    try
      LoadFromFile(sessionFile);
      JsonText := Text;
      WriteLn('JSON 文件大小: ', Length(JsonText), ' 字符');
    finally
      Free;
    end;

    // 查找最后一个 "content": "
    ContentStart := 0;
    i := Length(JsonText);
    while i > 0 do
    begin
      if (i > 12) and (Copy(JsonText, i-11, 12) = '"content": "') then
      begin
        ContentStart := i + 1; // 指向内容开始位置
        Break;
      end;
      Dec(i);
    end;

    if ContentStart > 0 then
    begin
      WriteLn('找到最后一个 content 位置: ', ContentStart);

      // 查找内容结束位置 (下一个双引号)
      ContentEnd := ContentStart;
      while (ContentEnd <= Length(JsonText)) and (JsonText[ContentEnd] <> '"') do
      begin
        if JsonText[ContentEnd] = '\' then
          Inc(ContentEnd); // 跳过转义字符
        Inc(ContentEnd);
      end;

      if ContentEnd <= Length(JsonText) then
      begin
        RawContent := Copy(JsonText, ContentStart, ContentEnd - ContentStart);
        WriteLn('提取的原始内容长度: ', Length(RawContent));
        WriteLn('原始内容: ', RawContent);

        // 解码 Unicode 转义序列
        Result := DecodeUnicodeEscapes(RawContent);
      end;
    end;

  except
    on E: Exception do
    begin
      WriteLn('从原始 JSON 提取时出错: ', E.Message);
      LogToFile('从原始 JSON 提取时出错: ' + E.Message);
    end;
  end;
end;

function ExtractLatestResponse(const sessionFile: string): string;
var
  JsonText: string;
  JsonData: TJSONData;
  JsonObject: TJSONObject;
  MessageHistory: TJSONArray;
  LastMessage: TJSONObject;
  Parts: TJSONArray;
  i, j: Integer;
  Part: TJSONObject;
  Content, PartKind: string;
  FoundResponses: Integer;
begin
  Result := '';
  FoundResponses := 0;

  try
    WriteLn('开始解析会话文件...');

    // 读取 JSON 文件
    with TStringList.Create do
    try
      LoadFromFile(sessionFile);
      JsonText := Text;
      WriteLn('JSON 文件大小: ', Length(JsonText), ' 字符');
    finally
      Free;
    end;

    // 解析 JSON
    JsonData := GetJSON(JsonText);
    try
      JsonObject := JsonData as TJSONObject;
      MessageHistory := JsonObject.FindPath('message_history') as TJSONArray;
      WriteLn('消息历史数量: ', MessageHistory.Count);

      // 从后往前查找最后一个 assistant 回复
      for i := MessageHistory.Count - 1 downto 0 do
      begin
        LastMessage := MessageHistory[i] as TJSONObject;
        WriteLn('检查消息 ', i + 1, ', 类型: ', LastMessage.Get('kind'));

        if LastMessage.Get('kind') = 'response' then
        begin
          Inc(FoundResponses);
          WriteLn('找到第 ', FoundResponses, ' 个回复消息');

          Parts := LastMessage.FindPath('parts') as TJSONArray;
          if Assigned(Parts) then
          begin
            WriteLn('该回复有 ', Parts.Count, ' 个部分');

            for j := 0 to Parts.Count - 1 do
            begin
              Part := Parts[j] as TJSONObject;
              PartKind := Part.Get('part_kind');
              WriteLn('  部分 ', j + 1, ' 类型: ', PartKind);

              if PartKind = 'text' then
              begin
                Content := Part.Get('content');
                WriteLn('  文本内容长度: ', Length(Content));
                WriteLn('  原始内容: ', Content);

                // 检查是否包含 Unicode 转义序列
                if Pos('\u', Content) > 0 then
                begin
                  WriteLn('  发现 Unicode 转义序列，开始解码...');
                  Content := DecodeUnicodeEscapes(Content);
                  WriteLn('  解码后内容: ', Content);
                end
                else
                begin
                  WriteLn('  内容已经是解码后的文本，无需转换');
                  // JSON 解析器已经自动处理了 Unicode 转义，内容应该是正确的
                  // 但可能有控制台显示编码问题
                end;

                if Content <> '' then
                begin
                  WriteLn('找到文本内容，返回结果');
                  Result := Content;
                  Exit;
                end;
              end;
            end;
          end
          else
            WriteLn('该回复没有 parts 数组');
        end;
      end;

      WriteLn('总共找到 ', FoundResponses, ' 个回复消息');
      if Result = '' then
        WriteLn('没有找到有效的文本回复内容');

    finally
      JsonData.Free;
    end;

  except
    on E: Exception do
    begin
      WriteLn('Error reading session file: ', E.Message);
      LogToFile('解析会话文件时出错: ' + E.Message);
    end;
  end;
end;

function QueryRovoDevWithSession(const question: string): string;
var
  Process: TProcess;
  ExitCode: Integer;
  SessionDir: string;
  Response: string;
  BeforeCount, AfterCount: Integer;
  ConfigFile: string;
  RovoConfig: TRovoDevConfig;
begin
  Result := '';

  WriteLn('=== 发送问题到 RovoDev ===');
  WriteLn('问题: ', question);

  // 使用 custom_persistence_config.yml 在现有会话中添加新消息
  ConfigFile := 'custom_persistence_config.yml';
  RovoConfig := LoadRovoDevConfig(ConfigFile);

  if not RovoConfig.IsValid then
  begin
    WriteLn('配置文件加载失败，使用默认配置');
    ConfigFile := 'minimal_config.yml';
    RovoConfig.SessionsDir := 'ai_dev_sessions';
  end;

  // 查找现有会话并记录当前状态
  if not DirectoryExists(RovoConfig.SessionsDir) then
  begin
    WriteLn('会话目录不存在: ', RovoConfig.SessionsDir);
    Exit;
  end;

  SessionDir := FindLatestSessionDir(RovoConfig.SessionsDir);
  if SessionDir <> '' then
  begin
    SessionDir := ExtractFileDir(SessionDir);
    WriteLn('使用会话目录: ', SessionDir);
    WriteLn('会话文件: ', SessionDir + '\session_context.json');

    // 记录执行前的会话文件修改时间
    WriteLn('记录执行前会话状态...');
  end
  else
  begin
    WriteLn('未找到现有会话目录');
    WriteLn('会话基础目录: ', RovoConfig.SessionsDir);
    Exit;
  end;

  // 使用 TProcess 执行 RovoDev 命令
  WriteLn('执行 RovoDev 命令...');
  Process := TProcess.Create(nil);
  try
    Process.Executable := 'C:\test\acli.exe';
    Process.Parameters.Add('rovodev');
    Process.Parameters.Add('run');
    Process.Parameters.Add('--config-file');
    Process.Parameters.Add(ConfigFile);
    Process.Parameters.Add(question);

    // 允许控制台交互，但等待完成
    Process.Options := [poWaitOnExit];

    WriteLn('命令行: ', Process.Executable, ' ', Process.Parameters.CommaText);
    Process.Execute;
    ExitCode := Process.ExitStatus;
  finally
    Process.Free;
  end;

  WriteLn('命令执行完成，退出代码: ', ExitCode);

  // 等待会话文件更新 - 增加等待时间
  WriteLn('等待会话文件更新...');
  Sleep(5000);

  // 强制刷新获取最新回复
  WriteLn('获取最新回复...');
  Response := ProcessSessionResponse(SessionDir, True);

  if Response <> '' then
  begin
    WriteLn('=== 成功获取回复 ===');
    Result := Response;
  end
  else
    WriteLn('未能获取回复内容');
end;
  
begin
  WriteLn('=== SessionReader 测试程序 ===');
  WriteLn('使用 SessionUtils 处理 RovoDev 会话');
  WriteLn;

  Question := '你好';

  LogToFile('=== 开始 SessionUtils 测试 ===');
  LogToFile('问题: ' + Question);

  Response := QueryRovoDevWithSession(Question);
  
  if Response <> '' then
  begin
    WriteLn;
    WriteLn('=== RESPONSE SUCCESSFULLY CAPTURED ===');
    WriteLn('完整回复内容: ', Response);
    WriteLn('=== END OF RESPONSE ===');
    WriteLn;
    WriteLn('=== RESPONSE CONTENT ANALYSIS ===');
    WriteLn('Response Length: ', Length(Response), ' characters');
    WriteLn('完整内容: ', Response);
    WriteLn('Response saved to latest_response_utf8.txt for proper viewing');
    WriteLn('=== END OF ANALYSIS ===');

    LogToFile('=== 成功获取回复 ===');
    LogToFile('回复长度: ' + IntToStr(Length(Response)) + ' 字符');
    LogToFile('完整回复内容:');
    LogToFile(Response);

    // 将回复内容保存为UTF-8文件
    try
      with TStringList.Create do
      try
        Add(Response);
        SaveToFile('latest_response_utf8.txt');
        LogToFile('回复内容已保存到 latest_response_utf8.txt');
        WriteLn('回复内容已保存到文件，请用文本编辑器查看正确内容');
      finally
        Free;
      end;
    except
      on E: Exception do
        LogToFile('保存UTF-8文件时出错: ' + E.Message);
    end;

    LogToFile('=== 回复结束 ===');
  end
  else
  begin
    WriteLn('Failed to capture response');
    LogToFile('获取回复失败');
  end;
  
  LogToFile('=== 测试结束 ===');
  WriteLn;
  WriteLn('Test completed. Check session_reader.log for details.');
  Sleep(3000);
end.
