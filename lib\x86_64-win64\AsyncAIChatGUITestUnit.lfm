object AsyncAIChatTestForm: TAsyncAIChatTestForm
  Left = 372
  Height = 500
  Top = 124
  Width = 600
  Caption = 'AsyncAIChat 组件测试'
  ClientHeight = 500
  ClientWidth = 600
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  LCLVersion = '2.2.6.0'
  object Panel1: TPanel
    Left = 0
    Height = 50
    Top = 450
    Width = 600
    Align = alBottom
    ClientHeight = 50
    ClientWidth = 600
    TabOrder = 0
  end
  object Panel2: TPanel
    Left = 0
    Height = 450
    Top = 0
    Width = 600
    Align = alClient
    ClientHeight = 450
    ClientWidth = 600
    TabOrder = 1
  end
  object InputEdit: TEdit
    Left = 10
    Height = 23
    Top = 15
    Width = 400
    TabOrder = 2
  end
  object SendButton: TButton
    Left = 420
    Height = 25
    Top = 13
    Width = 80
    Caption = '发送'
    OnClick = SendButtonClick
    TabOrder = 3
  end
  object ChatMemo: TMemo
    Left = 0
    Height = 450
    Top = 0
    Width = 600
    Align = alClient
    ReadOnly = True
    ScrollBars = ssVertical
    TabOrder = 4
  end
  object StatusLabel: TLabel
    Left = 510
    Height = 15
    Top = 17
    Width = 24
    Caption = '就绪'
  end
end
