unit AsyncAIChat;

{$mode objfpc}{$H+}
{$codepage utf8}

{
  ===============================================================================
  AsyncAIChat - 异步AI对话通用组件
  ===============================================================================
  
  功能：
    提供一个通用的异步AI对话解决方案，支持任何基于会话文件的AI工具
  
  特性：
    ✅ 完全异步，不阻塞界面
    ✅ 实时监控，快速响应
    ✅ 无竞态条件
    ✅ 线程安全
    ✅ 易于集成
    ✅ 错误处理完善
  
  使用方法：
    1. 创建 TAsyncAIChat 实例
    2. 设置回调事件
    3. 调用 SendMessage 发送消息
    4. 在回调中处理回复
  
  作者：AI Assistant (Augment Agent)
  日期：2025-07-17
  版本：1.0
  ===============================================================================
}

interface

uses
  Classes, SysUtils, Process, SessionUtils;

type
  // 前向声明
  TAsyncAIChat = class;
  
  // 事件类型定义
  TAIResponseEvent = procedure(const Response: string) of object;
  TAIErrorEvent = procedure(const ErrorMsg: string) of object;
  TAIStatusEvent = procedure(const Status: string) of object;
  
  // 会话监控线程
  TAIChatMonitorThread = class(TThread)
  private
    FChat: TAsyncAIChat;
    FSessionDir: string;
    FLastMessageCount: Integer;
  protected
    procedure Execute; override;
    procedure NotifyNewMessage;
  public
    constructor Create(AChat: TAsyncAIChat; const ASessionDir: string);
    procedure UpdateSessionDir(const ASessionDir: string);
  end;
  
  // 异步AI对话主类
  TAsyncAIChat = class
  private
    FMonitorThread: TAIChatMonitorThread;
    FSessionDir: string;
    FPendingMessageCount: Integer;
    FIsWaitingForReply: Boolean;
    FExecutablePath: string;
    FConfigFile: string;
    
    // 事件
    FOnResponse: TAIResponseEvent;
    FOnError: TAIErrorEvent;
    FOnStatus: TAIStatusEvent;
    
    procedure DoResponse(const Response: string);
    procedure DoError(const ErrorMsg: string);
    procedure DoStatus(const Status: string);
    
  public
    constructor Create;
    destructor Destroy; override;
    
    // 配置方法
    procedure SetExecutable(const ExecutablePath: string);
    procedure SetConfigFile(const ConfigFilePath: string);
    procedure SetSessionDir(const SessionDirPath: string);
    
    // 核心方法
    function SendMessage(const Message: string): Boolean;
    procedure StartMonitoring;
    procedure StopMonitoring;
    
    // 内部方法（由监控线程调用）
    procedure OnNewMessageDetected;
    
    // 属性
    property SessionDir: string read FSessionDir;
    property IsWaitingForReply: Boolean read FIsWaitingForReply;
    
    // 事件
    property OnResponse: TAIResponseEvent read FOnResponse write FOnResponse;
    property OnError: TAIErrorEvent read FOnError write FOnError;
    property OnStatus: TAIStatusEvent read FOnStatus write FOnStatus;
  end;

implementation

// ===== TAIChatMonitorThread 实现 =====

constructor TAIChatMonitorThread.Create(AChat: TAsyncAIChat; const ASessionDir: string);
begin
  inherited Create(False);
  FChat := AChat;
  FSessionDir := ASessionDir;
  FLastMessageCount := 0;
  FreeOnTerminate := True;
end;

procedure TAIChatMonitorThread.UpdateSessionDir(const ASessionDir: string);
begin
  FSessionDir := ASessionDir;
  FLastMessageCount := 0;
end;

procedure TAIChatMonitorThread.Execute;
var
  SessionInfo: TSessionInfo;
  CurrentMessageCount: Integer;
  SessionFile: string;
begin
  while not Terminated do
  begin
    try
      if (FSessionDir <> '') and DirectoryExists(FSessionDir) then
      begin
        SessionFile := FSessionDir + '\session_context.json';
        if FileExists(SessionFile) then
        begin
          try
            SessionInfo := GetSessionInfo(FSessionDir);
            if SessionInfo.IsValid then
            begin
              CurrentMessageCount := SessionInfo.MessageCount;
              
              if (FLastMessageCount > 0) and (CurrentMessageCount > FLastMessageCount) then
              begin
                FLastMessageCount := CurrentMessageCount;
                Synchronize(@NotifyNewMessage);
              end
              else if FLastMessageCount = 0 then
              begin
                FLastMessageCount := CurrentMessageCount;
              end;
            end;
          except
            // 忽略读取错误，继续监控
          end;
        end;
      end;
      
      Sleep(2000); // 每2秒检查一次
      
    except
      // 忽略所有异常，确保监控线程持续运行
    end;
  end;
end;

procedure TAIChatMonitorThread.NotifyNewMessage;
begin
  if Assigned(FChat) then
  begin
    FChat.OnNewMessageDetected;
  end;
end;

// ===== TAsyncAIChat 实现 =====

constructor TAsyncAIChat.Create;
begin
  inherited Create;
  FMonitorThread := nil;
  FSessionDir := '';
  FPendingMessageCount := 0;
  FIsWaitingForReply := False;
  FExecutablePath := '';
  FConfigFile := '';
end;

destructor TAsyncAIChat.Destroy;
begin
  StopMonitoring;
  inherited Destroy;
end;

procedure TAsyncAIChat.SetExecutable(const ExecutablePath: string);
begin
  FExecutablePath := ExecutablePath;
end;

procedure TAsyncAIChat.SetConfigFile(const ConfigFilePath: string);
begin
  FConfigFile := ConfigFilePath;
end;

procedure TAsyncAIChat.SetSessionDir(const SessionDirPath: string);
begin
  FSessionDir := SessionDirPath;
  if Assigned(FMonitorThread) then
  begin
    FMonitorThread.UpdateSessionDir(SessionDirPath);
  end;
end;

procedure TAsyncAIChat.DoResponse(const Response: string);
begin
  if Assigned(FOnResponse) then
    FOnResponse(Response);
end;

procedure TAsyncAIChat.DoError(const ErrorMsg: string);
begin
  if Assigned(FOnError) then
    FOnError(ErrorMsg);
end;

procedure TAsyncAIChat.DoStatus(const Status: string);
begin
  if Assigned(FOnStatus) then
    FOnStatus(Status);
end;

procedure TAsyncAIChat.StartMonitoring;
begin
  StopMonitoring;
  
  if FSessionDir <> '' then
  begin
    FMonitorThread := TAIChatMonitorThread.Create(Self, FSessionDir);
    DoStatus('监控线程已启动');
  end;
end;

procedure TAsyncAIChat.StopMonitoring;
begin
  if Assigned(FMonitorThread) then
  begin
    FMonitorThread.Terminate;
    FMonitorThread := nil;
    DoStatus('监控线程已停止');
  end;
end;

function TAsyncAIChat.SendMessage(const Message: string): Boolean;
var
  Process: TProcess;
  SessionInfo: TSessionInfo;
  WaitCount: Integer;
  ExitCode: Integer;
begin
  Result := False;
  
  if (FExecutablePath = '') or (FSessionDir = '') then
  begin
    DoError('未设置可执行文件路径或会话目录');
    Exit;
  end;
  
  try
    DoStatus('准备发送消息...');
    
    // 关键步骤：提前设置等待状态
    try
      SessionInfo := GetSessionInfo(FSessionDir);
      if SessionInfo.IsValid then
      begin
        FPendingMessageCount := SessionInfo.MessageCount;
        FIsWaitingForReply := True;
        DoStatus('等待状态已设置，基准消息数量: ' + IntToStr(FPendingMessageCount));
      end
      else
      begin
        DoError('无法获取会话信息');
        Exit;
      end;
    except
      on E: Exception do
      begin
        DoError('设置等待状态失败: ' + E.Message);
        Exit;
      end;
    end;
    
    // 创建并执行AI进程
    Process := TProcess.Create(nil);
    try
      Process.Executable := FExecutablePath;
      Process.Parameters.Add('rovodev');
      Process.Parameters.Add('run');
      
      if FConfigFile <> '' then
      begin
        Process.Parameters.Add('--config-file');
        Process.Parameters.Add(FConfigFile);
      end;
      
      Process.Parameters.Add(Message);
      Process.ShowWindow := swoHide;
      Process.Options := [poNoConsole];
      
      DoStatus('正在发送消息到AI...');
      Process.Execute;
      
      // 等待进程完成
      WaitCount := 0;
      while Process.Running and (WaitCount < 600) do
      begin
        Sleep(100);
        Inc(WaitCount);
        if WaitCount mod 50 = 0 then
          DoStatus('AI处理中... ' + IntToStr(WaitCount div 10) + '秒');
      end;
      
      if Process.Running then
      begin
        Process.Terminate(1);
        ExitCode := -2;
      end
      else
      begin
        ExitCode := Process.ExitStatus;
      end;
      
    finally
      Process.Free;
    end;
    
    if ExitCode = 0 then
    begin
      DoStatus('消息发送成功，等待AI回复...');
      Result := True;
    end
    else
    begin
      DoError('消息发送失败，退出代码: ' + IntToStr(ExitCode));
      FIsWaitingForReply := False;
      FPendingMessageCount := 0;
    end;
    
  except
    on E: Exception do
    begin
      DoError('发送消息时出错: ' + E.Message);
      FIsWaitingForReply := False;
      FPendingMessageCount := 0;
    end;
  end;
end;

procedure TAsyncAIChat.OnNewMessageDetected;
var
  SessionInfo: TSessionInfo;
  Response: string;
begin
  if not FIsWaitingForReply then
    Exit;
    
  try
    SessionInfo := GetSessionInfo(FSessionDir);
    
    if SessionInfo.IsValid and (SessionInfo.MessageCount > FPendingMessageCount) then
    begin
      DoStatus('检测到新回复，正在提取...');
      
      Response := ExtractLatestResponseFromJSON(FSessionDir + '\session_context.json');
      
      if Response <> '' then
      begin
        // 重置等待状态
        FIsWaitingForReply := False;
        FPendingMessageCount := 0;
        
        DoStatus('回复提取成功');
        DoResponse(Response);
      end
      else
      begin
        DoError('回复提取失败');
      end;
    end;
    
  except
    on E: Exception do
    begin
      DoError('处理新消息时出错: ' + E.Message);
    end;
  end;
end;

end.
