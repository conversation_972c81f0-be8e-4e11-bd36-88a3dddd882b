unit SessionUtils;

{$mode objfpc}{$H+}
{$codepage utf8}

interface

uses
  SysUtils, Classes, LazUTF8, fpjson, jsonparser;

type
  TSessionInfo = record
    SessionID: string;
    LastModified: TDateTime;
    MessageCount: Integer;
    CacheFile: string;
    IsValid: Boolean;
  end;

  TMessagePair = record
    Question: string;
    Response: string;
    QuestionTime: TDateTime;
    ResponseTime: TDateTime;
    IsValid: Boolean;
  end;

// 主要的会话处理函数
function ProcessSessionResponse(const sessionDir: string; forceRefresh: Boolean = False): string;

// 获取最新的问题-回复对
function GetLatestQuestionResponse(const sessionDir: string): TMessagePair;

// 获取指定问题的回复
function GetResponseForQuestion(const sessionDir: string; const question: string): string;

// 辅助函数
function GetSessionInfo(const sessionDir: string): TSessionInfo;
function DecodeUnicodeEscapes(const input: string): string;
function ExtractLatestResponseFromJSON(const jsonFile: string): string;
function IsCacheValid(const sessionDir: string; const cacheFile: string): Boolean;

implementation

function DecodeUnicodeEscapes(const input: string): string;
var
  i: Integer;
  unicodeStr: string;
  unicodeValue: Cardinal;
  utf8Char: string;
  FoundCount: Integer;
begin
  // 修改时间: 2025-01-09 20:50:00 - 添加详细调试日志
  WriteLn('[SessionUtils] 🔧 DecodeUnicodeEscapes 开始，输入长度: ', Length(input));
  WriteLn('[SessionUtils] 🔧 输入前50字符: "', Copy(input, 1, 50), '"');

  Result := input;
  i := 1;
  FoundCount := 0;

  while i <= Length(Result) - 5 do
  begin
    if (Result[i] = '\') and (Result[i+1] = 'u') then
    begin
      Inc(FoundCount);
      unicodeStr := Copy(Result, i+2, 4);
      WriteLn('[SessionUtils] 🔧 找到Unicode序列 #', FoundCount, ': \u', unicodeStr);
      try
        unicodeValue := StrToInt('$' + unicodeStr);
        utf8Char := UnicodeToUTF8(unicodeValue);
        WriteLn('[SessionUtils] 🔧 解码: \u', unicodeStr, ' -> "', utf8Char, '"');
        Delete(Result, i, 6);
        Insert(utf8Char, Result, i);
        Inc(i, Length(utf8Char));
      except
        WriteLn('[SessionUtils] ❌ Unicode解码失败: \u', unicodeStr);
        Inc(i);
      end;
    end
    else
      Inc(i);
  end;

  WriteLn('[SessionUtils] 🔧 DecodeUnicodeEscapes 完成，找到 ', FoundCount, ' 个Unicode序列');
  WriteLn('[SessionUtils] 🔧 输出长度: ', Length(Result));
  WriteLn('[SessionUtils] 🔧 输出前50字符: "', Copy(Result, 1, 50), '"');
end;

function ExtractLatestResponseFromJSON(const jsonFile: string): string;
var
  JsonText: string;
  i: Integer;
  ContentStart, ContentEnd: Integer;
  RawContent: string;
begin
  Result := '';
  
  try
    // 读取 JSON 文件
    with TStringList.Create do
    try
      LoadFromFile(jsonFile);
      JsonText := Text;
    finally
      Free;
    end;
    
    // 查找最后一个 "content": "
    ContentStart := 0;
    i := Length(JsonText);
    while i > 0 do
    begin
      if (i > 12) and (Copy(JsonText, i-11, 12) = '"content": "') then
      begin
        ContentStart := i + 1;
        Break;
      end;
      Dec(i);
    end;
    
    if ContentStart > 0 then
    begin
      // 查找内容结束位置
      ContentEnd := ContentStart;
      while (ContentEnd <= Length(JsonText)) and (JsonText[ContentEnd] <> '"') do
      begin
        if JsonText[ContentEnd] = '\' then
          Inc(ContentEnd);
        Inc(ContentEnd);
      end;
      
      if ContentEnd <= Length(JsonText) then
      begin
        RawContent := Copy(JsonText, ContentStart, ContentEnd - ContentStart);
        // 解码 Unicode 转义序列
        Result := DecodeUnicodeEscapes(RawContent);
      end;
    end;
    
  except
    on E: Exception do
      WriteLn('提取回复时出错: ', E.Message);
  end;
end;

function GetSessionInfo(const sessionDir: string): TSessionInfo;
var
  sessionFile: string;
  JsonText: string;
  messageCount: Integer;
  pos1, pos2: Integer;
begin
  Result.SessionID := ExtractFileName(sessionDir);
  Result.LastModified := 0;
  Result.MessageCount := 0;
  Result.CacheFile := sessionDir + '_response_cache.txt';
  Result.IsValid := False;
  
  sessionFile := sessionDir + '\session_context.json';
  
  if FileExists(sessionFile) then
  begin
    Result.LastModified := FileDateToDateTime(FileAge(sessionFile));
    
    // 快速计算消息数量
    try
      with TStringList.Create do
      try
        LoadFromFile(sessionFile);
        JsonText := Text;
        
        // 计算 "kind": 出现的次数来估算消息数量
        messageCount := 0;
        pos1 := 1;
        while pos1 > 0 do
        begin
          pos1 := Pos('"kind":', JsonText, pos1);
          if pos1 > 0 then
          begin
            Inc(messageCount);
            Inc(pos1, 7);
          end;
        end;
        
        Result.MessageCount := messageCount;
        Result.IsValid := True;
      finally
        Free;
      end;
    except
      // 如果出错，保持默认值
    end;
  end;
end;

function IsCacheValid(const sessionDir: string; const cacheFile: string): Boolean;
var
  sessionFile: string;
  sessionTime, cacheTime: TDateTime;
begin
  Result := False;
  
  if not FileExists(cacheFile) then
    Exit;
    
  sessionFile := sessionDir + '\session_context.json';
  if not FileExists(sessionFile) then
    Exit;
    
  sessionTime := FileDateToDateTime(FileAge(sessionFile));
  cacheTime := FileDateToDateTime(FileAge(cacheFile));
  
  // 如果缓存文件比会话文件新，则缓存有效
  Result := cacheTime >= sessionTime;
end;

function ProcessSessionResponse(const sessionDir: string; forceRefresh: Boolean = False): string;
var
  sessionInfo: TSessionInfo;
  sessionFile: string;
  response: string;
  cacheList: TStringList;
  logMsg: string;
begin
  Result := '';
  
  WriteLn('=== 处理会话回复 ===');
  WriteLn('会话目录: ', sessionDir);
  
  // 获取会话信息
  sessionInfo := GetSessionInfo(sessionDir);
  
  if not sessionInfo.IsValid then
  begin
    WriteLn('错误: 无效的会话目录');
    Exit;
  end;
  
  WriteLn('会话ID: ', sessionInfo.SessionID);
  WriteLn('最后修改: ', DateTimeToStr(sessionInfo.LastModified));
  WriteLn('消息数量: ', sessionInfo.MessageCount);
  WriteLn('缓存文件: ', sessionInfo.CacheFile);
  
  // 检查缓存是否有效
  if (not forceRefresh) and IsCacheValid(sessionDir, sessionInfo.CacheFile) then
  begin
    WriteLn('使用缓存的回复内容...');
    try
      with TStringList.Create do
      try
        LoadFromFile(sessionInfo.CacheFile);
        if Count > 0 then
        begin
          Result := Strings[0];
          WriteLn('从缓存加载成功，内容长度: ', Length(Result));
          WriteLn('缓存内容: ', Result);
          WriteLn('=== 处理完成 (使用缓存) ===');
          WriteLn('');
          Exit;
        end;
      finally
        Free;
      end;
    except
      WriteLn('读取缓存失败，将重新提取...');
    end;
  end
  else if forceRefresh then
    WriteLn('强制刷新，忽略缓存...')
  else
    WriteLn('缓存无效或不存在，从会话文件提取...');
  
  // 从会话文件提取最新回复
  WriteLn('从会话文件提取最新回复...');
  sessionFile := sessionDir + '\session_context.json';
  response := ExtractLatestResponseFromJSON(sessionFile);
  
  if response <> '' then
  begin
    WriteLn('提取成功！');
    WriteLn('回复长度: ', Length(response), ' 字符');
    WriteLn('回复内容: ', response);
    
    // 保存到缓存文件
    try
      cacheList := TStringList.Create;
      try
        cacheList.Add(response);
        cacheList.Add('');
        cacheList.Add('# 缓存信息');
        cacheList.Add('# 会话ID: ' + sessionInfo.SessionID);
        cacheList.Add('# 提取时间: ' + DateTimeToStr(Now));
        cacheList.Add('# 消息数量: ' + IntToStr(sessionInfo.MessageCount));
        cacheList.Add('# 回复长度: ' + IntToStr(Length(response)) + ' 字符');
        
        cacheList.SaveToFile(sessionInfo.CacheFile);
        WriteLn('回复已保存到缓存: ', sessionInfo.CacheFile);
      finally
        cacheList.Free;
      end;
    except
      on E: Exception do
        WriteLn('保存缓存失败: ', E.Message);
    end;
    
    Result := response;
  end
  else
  begin
    WriteLn('未能提取到回复内容');
  end;
  
  WriteLn('=== 处理完成 ===');
  WriteLn('');
end;

function GetLatestQuestionResponse(const sessionDir: string): TMessagePair;
var
  sessionFile: string;
  JsonText: string;
  JsonData: TJSONData;
  JsonObject: TJSONObject;
  MessageHistory: TJSONArray;
  i, j: Integer;
  Message: TJSONObject;
  Parts: TJSONArray;
  Part: TJSONObject;
  MessageKind, Content: string;
  LastQuestion, LastResponse: string;
begin
  Result.Question := '';
  Result.Response := '';
  Result.QuestionTime := 0;
  Result.ResponseTime := 0;
  Result.IsValid := False;

  sessionFile := sessionDir + '\session_context.json';

  if not FileExists(sessionFile) then
    Exit;

  try
    // 读取 JSON 文件
    with TStringList.Create do
    try
      LoadFromFile(sessionFile);
      JsonText := Text;
    finally
      Free;
    end;

    // 解析 JSON
    JsonData := GetJSON(JsonText);
    try
      JsonObject := JsonData as TJSONObject;
      MessageHistory := JsonObject.FindPath('message_history') as TJSONArray;

      // 从后往前查找最后一对问题-回复
      for i := MessageHistory.Count - 1 downto 0 do
      begin
        Message := MessageHistory[i] as TJSONObject;
        MessageKind := Message.Get('kind');

        if MessageKind = 'response' then
        begin
          // 找到回复，提取内容
          Parts := Message.FindPath('parts') as TJSONArray;
          if Assigned(Parts) and (Parts.Count > 0) then
          begin
            Part := Parts[0] as TJSONObject;
            if Part.Get('part_kind') = 'text' then
            begin
              Content := Part.Get('content');
              if Content <> '' then
              begin
                LastResponse := Content;

                // 继续往前找对应的问题
                for j := i - 1 downto 0 do
                begin
                  Message := MessageHistory[j] as TJSONObject;
                  if Message.Get('kind') = 'request' then
                  begin
                    Parts := Message.FindPath('parts') as TJSONArray;
                    if Assigned(Parts) and (Parts.Count > 0) then
                    begin
                      Part := Parts[0] as TJSONObject;
                      if Part.Get('part_kind') = 'text' then
                      begin
                        Content := Part.Get('content');
                        if Content <> '' then
                        begin
                          LastQuestion := Content;

                          Result.Question := LastQuestion;
                          Result.Response := LastResponse;
                          Result.IsValid := True;
                          Exit;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end;
        end;
      end;

    finally
      JsonData.Free;
    end;

  except
    on E: Exception do
      WriteLn('解析会话文件时出错: ', E.Message);
  end;
end;

function GetResponseForQuestion(const sessionDir: string; const question: string): string;
var
  sessionFile: string;
  JsonText: string;
  JsonData: TJSONData;
  JsonObject: TJSONObject;
  MessageHistory: TJSONArray;
  i, j: Integer;
  Message: TJSONObject;
  Parts: TJSONArray;
  Part: TJSONObject;
  MessageKind, Content: string;
  FoundQuestion: Boolean;
begin
  Result := '';
  FoundQuestion := False;

  sessionFile := sessionDir + '\session_context.json';

  if not FileExists(sessionFile) then
    Exit;

  try
    // 读取 JSON 文件
    with TStringList.Create do
    try
      LoadFromFile(sessionFile);
      JsonText := Text;
    finally
      Free;
    end;

    // 解析 JSON
    JsonData := GetJSON(JsonText);
    try
      JsonObject := JsonData as TJSONObject;
      MessageHistory := JsonObject.FindPath('message_history') as TJSONArray;

      // 查找指定的问题及其回复
      for i := 0 to MessageHistory.Count - 1 do
      begin
        Message := MessageHistory[i] as TJSONObject;
        MessageKind := Message.Get('kind');

        if MessageKind = 'request' then
        begin
          // 检查是否是目标问题
          Parts := Message.FindPath('parts') as TJSONArray;
          if Assigned(Parts) and (Parts.Count > 0) then
          begin
            Part := Parts[0] as TJSONObject;
            if Part.Get('part_kind') = 'text' then
            begin
              Content := Part.Get('content');
              if Pos(question, Content) > 0 then
              begin
                FoundQuestion := True;
                // 继续往后找对应的回复
                for j := i + 1 to MessageHistory.Count - 1 do
                begin
                  Message := MessageHistory[j] as TJSONObject;
                  if Message.Get('kind') = 'response' then
                  begin
                    Parts := Message.FindPath('parts') as TJSONArray;
                    if Assigned(Parts) and (Parts.Count > 0) then
                    begin
                      Part := Parts[0] as TJSONObject;
                      if Part.Get('part_kind') = 'text' then
                      begin
                        Content := Part.Get('content');
                        if Content <> '' then
                        begin
                          // 修改时间: 2025-01-09 21:05:00 - 添加Unicode解码，与ExtractLatestResponseFromJSON保持一致
                          WriteLn('[SessionUtils] 🔧 GetResponseForQuestion 找到回复，开始Unicode解码');
                          Result := DecodeUnicodeEscapes(Content);
                          Exit;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end;
        end;
      end;

    finally
      JsonData.Free;
    end;

  except
    on E: Exception do
      WriteLn('解析会话文件时出错: ', E.Message);
  end;
end;

end.
