unit AsyncAIChatGUITestUnit;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  AsyncAIChat;

type
  TAsyncAIChatTestForm = class(TForm)
    InputEdit: TEdit;
    SendButton: TButton;
    ChatMemo: TMemo;
    StatusLabel: TLabel;
    Panel1: TPanel;
    Panel2: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure SendButtonClick(Sender: TObject);
  private
    FAIChat: TAsyncAIChat;
    
    // AI事件处理方法
    procedure OnAIResponse(const Response: string);
    procedure OnAIError(const ErrorMsg: string);
    procedure OnAIStatus(const Status: string);
    
    // 辅助方法
    procedure AddChatMessage(const Speaker, Message: string);
    procedure SetUIEnabled(AEnabled: Boolean);
  end;

var
  AsyncAIChatTestForm: TAsyncAIChatTestForm;

implementation

{$R *.lfm}

// ===== 窗体事件处理 =====

procedure TAsyncAIChatTestForm.FormCreate(Sender: TObject);
begin
  // 设置窗体属性
  Caption := 'AsyncAIChat 组件测试';
  Width := 600;
  Height := 500;
  
  // 设置控件属性
  Panel1.Height := 50;
  Panel1.Align := alBottom;
  Panel2.Align := alClient;
  
  ChatMemo.Parent := Panel2;
  ChatMemo.Align := alClient;
  ChatMemo.ReadOnly := True;
  ChatMemo.ScrollBars := ssVertical;
  
  InputEdit.Parent := Panel1;
  InputEdit.Left := 10;
  InputEdit.Top := 15;
  InputEdit.Width := 400;
  
  SendButton.Parent := Panel1;
  SendButton.Left := 420;
  SendButton.Top := 13;
  SendButton.Width := 80;
  SendButton.Caption := '发送';
  
  StatusLabel.Parent := Panel1;
  StatusLabel.Left := 510;
  StatusLabel.Top := 17;
  StatusLabel.Caption := '就绪';
  
  // 创建异步AI对话组件
  FAIChat := TAsyncAIChat.Create;
  
  // 配置AI工具路径和参数
  FAIChat.SetExecutable('C:\test\acli.exe');
  FAIChat.SetConfigFile('custom_persistence_config.yml');
  FAIChat.SetSessionDir('custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2');
  
  // 设置事件回调
  FAIChat.OnResponse := @OnAIResponse;
  FAIChat.OnError := @OnAIError;
  FAIChat.OnStatus := @OnAIStatus;
  
  // 启动监控
  FAIChat.StartMonitoring;
  
  // 初始化界面
  ChatMemo.Clear;
  AddChatMessage('系统', 'AsyncAIChat 组件测试程序已启动');
  AddChatMessage('系统', '监控线程已启动，可以开始对话');
end;

procedure TAsyncAIChatTestForm.FormDestroy(Sender: TObject);
begin
  // 清理资源
  if Assigned(FAIChat) then
  begin
    FAIChat.StopMonitoring;
    FAIChat.Free;
  end;
end;

procedure TAsyncAIChatTestForm.SendButtonClick(Sender: TObject);
var
  Message: string;
begin
  Message := Trim(InputEdit.Text);
  if Message = '' then
    Exit;
    
  // 显示用户消息
  AddChatMessage('👤 用户', Message);
  
  // 禁用界面，防止重复发送
  SetUIEnabled(False);
  
  // 发送消息给AI（异步）
  if FAIChat.SendMessage(Message) then
  begin
    // 发送成功，清空输入框
    InputEdit.Clear;
  end
  else
  begin
    // 发送失败，重新启用界面
    SetUIEnabled(True);
  end;
end;

// ===== AI事件处理方法 =====

procedure TAsyncAIChatTestForm.OnAIResponse(const Response: string);
begin
  // 收到AI回复
  AddChatMessage('🤖 AI', Response);
  
  // 重新启用界面
  SetUIEnabled(True);
  
  // 设置焦点到输入框
  if InputEdit.CanFocus then
    InputEdit.SetFocus;
end;

procedure TAsyncAIChatTestForm.OnAIError(const ErrorMsg: string);
begin
  // 处理错误
  AddChatMessage('❌ 错误', ErrorMsg);
  
  // 重新启用界面
  SetUIEnabled(True);
end;

procedure TAsyncAIChatTestForm.OnAIStatus(const Status: string);
begin
  // 更新状态显示
  StatusLabel.Caption := Status;
  Application.ProcessMessages;
end;

// ===== 辅助方法 =====

procedure TAsyncAIChatTestForm.AddChatMessage(const Speaker, Message: string);
var
  TimeStr: string;
begin
  TimeStr := FormatDateTime('hh:nn:ss', Now);
  ChatMemo.Lines.Add(Format('[%s] %s: %s', [TimeStr, Speaker, Message]));
  ChatMemo.Lines.Add(''); // 空行分隔
  
  // 滚动到底部
  ChatMemo.SelStart := Length(ChatMemo.Text);
  ChatMemo.SelLength := 0;
end;

procedure TAsyncAIChatTestForm.SetUIEnabled(AEnabled: Boolean);
begin
  SendButton.Enabled := AEnabled;
  InputEdit.Enabled := AEnabled;

  if AEnabled then
    StatusLabel.Caption := '就绪'
  else
    StatusLabel.Caption := '处理中...';
end;

end.
