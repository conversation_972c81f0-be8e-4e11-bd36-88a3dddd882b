program TestSessionInfo;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils, SessionUtils;

var
  SessionDir: string;
  SessionInfo: TSessionInfo;

begin
  WriteLn('=== 测试 GetSessionInfo 功能 ===');
  
  SessionDir := 'custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2';
  WriteLn('会话目录: ', SessionDir);
  
  if DirectoryExists(SessionDir) then
    WriteLn('✅ 会话目录存在')
  else
  begin
    WriteLn('❌ 会话目录不存在');
    Exit;
  end;
  
  WriteLn('');
  WriteLn('=== 开始获取会话信息 ===');
  
  try
    SessionInfo := GetSessionInfo(SessionDir);
    
    if SessionInfo.IsValid then
    begin
      WriteLn('✅ 成功获取会话信息！');
      WriteLn('会话ID: ', SessionInfo.SessionID);
      WriteLn('消息数量: ', SessionInfo.MessageCount);
      WriteLn('是否有效: ', BoolToStr(SessionInfo.IsValid, True));
    end
    else
    begin
      WriteLn('❌ 获取会话信息失败 - 返回无效结果');
    end;
    
  except
    on E: Exception do
    begin
      WriteLn('❌ 获取会话信息出错: ', E.Message);
      WriteLn('错误类型: ', E.ClassName);
    end;
  end;
  
  WriteLn('');
  WriteLn('=== 测试完成 ===');
  WriteLn('按回车键退出...');
  ReadLn;
end.
