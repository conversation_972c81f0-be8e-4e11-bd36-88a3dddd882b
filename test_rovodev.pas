program TestRovoDev;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils, Process;

var
  RovoProcess: TProcess;
  ExitCode: Integer;
  SessionsDir: string;

begin
  WriteLn('=== 测试 RovoDev 连接 ===');
  
  // 检查会话目录
  SessionsDir := 'custom_sessions';
  WriteLn('检查会话目录: ', SessionsDir);
  
  if DirectoryExists(SessionsDir) then
    WriteLn('✅ 会话目录存在')
  else
    WriteLn('❌ 会话目录不存在');
  
  // 检查 acli.exe
  if FileExists('C:\test\acli.exe') then
    WriteLn('✅ acli.exe 存在')
  else
    WriteLn('❌ acli.exe 不存在');
  
  // 检查配置文件
  if FileExists('custom_persistence_config.yml') then
    WriteLn('✅ 配置文件存在')
  else
    WriteLn('❌ 配置文件不存在');
  
  WriteLn('');
  WriteLn('=== 执行 RovoDev 命令 ===');
  
  RovoProcess := TProcess.Create(nil);
  try
    RovoProcess.Executable := 'C:\test\acli.exe';
    RovoProcess.Parameters.Add('rovodev');
    RovoProcess.Parameters.Add('run');
    RovoProcess.Parameters.Add('--config-file');
    RovoProcess.Parameters.Add('custom_persistence_config.yml');
    RovoProcess.Parameters.Add('简单测试');

    WriteLn('命令: ', RovoProcess.Executable);
    WriteLn('参数: ', RovoProcess.Parameters.CommaText);

    // 允许控制台交互
    RovoProcess.Options := [poWaitOnExit];

    WriteLn('开始执行...');
    RovoProcess.Execute;
    ExitCode := RovoProcess.ExitStatus;

    WriteLn('执行完成，退出代码: ', ExitCode);
  finally
    RovoProcess.Free;
  end;
  
  WriteLn('');
  WriteLn('=== 测试完成 ===');
  WriteLn('按回车键退出...');
  ReadLn;
end.
