unit testunit1;

{$mode objfpc}{$H+}
{$codepage utf8}

{
  ===============================================================================
  异步RovoDev对话系统 - 基于守护线程的解决方案
  ===============================================================================

  问题背景：
    GUI程序需要调用外部AI工具(RovoDev)进行对话，但存在以下挑战：
    1. AI处理时间不确定，可能需要几秒到几十秒
    2. 传统同步调用会阻塞GUI界面
    3. 异步调用需要知道AI何时完成回复
    4. 轮询检查存在竞态条件问题

  解决方案架构：
    1. TSessionMonitorThread守护线程：持续监控会话文件变化
    2. 提前状态设置：在启动AI进程前设置等待状态，避免竞态条件
    3. 消息计数检测：通过消息数量变化判断是否有新回复
    4. 线程安全通信：使用Synchronize在主线程中处理回复
    5. 自动状态管理：检测到回复后自动重置状态并重新启用界面

  关键优势：
    ✅ 完全异步：发送消息后立即返回，不阻塞界面
    ✅ 实时响应：每2秒检查一次，快速检测新回复
    ✅ 无竞态条件：在进程启动前设置等待状态
    ✅ 线程安全：使用Synchronize确保GUI操作在主线程执行
    ✅ 自动恢复：处理完回复后自动重新启用界面
    ✅ 错误处理：完善的异常处理确保系统稳定性

  核心流程：
    1. 用户发送消息 → 禁用界面
    2. 设置等待状态(IsWaitingForReply=True) → 记录基准消息数量
    3. 启动AI进程 → 立即返回PENDING
    4. 监控线程检测到消息数量增加 → 通知主线程
    5. 主线程提取回复并显示 → 重置状态并重新启用界面

  作者：AI Assistant (Augment Agent)
  日期：2025-07-17
  版本：1.0 - 生产就绪版本
  ===============================================================================
}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Process, LazUTF8, SessionUtils;

type
  // 前向声明
  TForm1 = class;

  {
    ===== 异步RovoDev对话系统 - 守护线程监控方案 =====

    问题背景：
    - GUI程序调用外部AI工具时界面阻塞
    - 需要等待AI回复但不知道何时完成
    - 传统轮询方法存在竞态条件

    解决方案：
    1. 使用守护线程持续监控会话文件变化
    2. 在启动AI进程前设置等待状态（避免竞态条件）
    3. 通过消息数量变化检测新回复
    4. 使用Synchronize安全地在主线程中处理回复

    关键优势：
    - 完全异步，不阻塞界面
    - 实时响应，检测到回复立即处理
    - 无竞态条件，状态管理精确
    - 自动恢复，处理完回复后重新启用界面
  }

  // 会话监控线程 - 核心组件
  TSessionMonitorThread = class(TThread)
  private
    FForm: TForm1;                    // 主窗体引用
    FSessionDir: string;              // 监控的会话目录
    FLastMessageCount: Integer;       // 上次检查的消息数量
    FLastCheckTime: TDateTime;        // 上次检查时间
  protected
    procedure Execute; override;      // 线程主循环
    procedure NotifyNewMessage;       // 通知主线程有新消息（线程安全）
  public
    constructor Create(AForm: TForm1; const ASessionDir: string);
    procedure UpdateSessionDir(const ASessionDir: string);
  end;

  TForm1 = class(TForm)
  private
    ChatMemo: TMemo;
    InputEdit: TEdit;
    SendButton: TButton;
    StatusButton: TButton;
    StatusLabel: TLabel;
    InitTimer: TTimer;

    SessionDir: string;
    ConfigFile: string;
    ChatCount: Integer;

    // 认证状态相关
    UserEmail: string;
    AuthStatus: string;
    IsAuthenticated: Boolean;

    // ===== 会话监控相关 - 异步对话系统状态管理 =====
    MonitorThread: TSessionMonitorThread;  // 守护线程实例
    PendingMessageCount: Integer;          // 发送消息前的基准消息数量
    IsWaitingForReply: Boolean;            // 等待回复标志（关键：避免竞态条件）

    procedure CreateGUI;
    procedure SendButtonClick(Sender: TObject);
    procedure StatusButtonClick(Sender: TObject);
    procedure InputEditKeyPress(Sender: TObject; var Key: char);
    procedure FormShow(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure InitTimerTimer(Sender: TObject);
    function InitSession: Boolean;
    function SendToAI(const question: string): string;
    function CheckAuthStatus: string;
    procedure PerformInitialAuthCheck;
    procedure UpdateWindowTitle;

    procedure AddChatMessage(const speaker: string; const message: string);
    procedure ShowStatus(const status: string);

    // ===== 会话监控相关方法 - 异步对话系统核心方法 =====
    procedure OnNewMessageDetected;   // 处理监控线程检测到的新消息
    procedure StartSessionMonitoring; // 启动会话监控线程
    procedure StopSessionMonitoring;  // 停止会话监控线程

  public
    constructor Create(AOwner: TComponent); override;
  end;

var
  Form1: TForm1;

implementation

{$R *.lfm}

// ===== 会话监控线程实现 - 异步对话系统核心组件 =====

{
  TSessionMonitorThread 构造函数

  功能：创建并启动会话监控线程
  参数：
    - AForm: 主窗体引用，用于回调通知
    - ASessionDir: 要监控的会话目录路径

  关键设计：
    - inherited Create(False): 立即启动线程，不暂停
    - FreeOnTerminate := True: 线程结束时自动释放内存
}
constructor TSessionMonitorThread.Create(AForm: TForm1; const ASessionDir: string);
begin
  inherited Create(False); // 不暂停，立即开始执行
  FForm := AForm;
  FSessionDir := ASessionDir;
  FLastMessageCount := 0;
  FLastCheckTime := Now;
  FreeOnTerminate := True;
end;

procedure TSessionMonitorThread.UpdateSessionDir(const ASessionDir: string);
begin
  FSessionDir := ASessionDir;
  FLastMessageCount := 0; // 重置计数
end;

{
  TSessionMonitorThread.Execute - 线程主循环

  功能：持续监控会话文件的消息数量变化

  工作原理：
    1. 每2秒检查一次会话文件
    2. 比较当前消息数量与上次记录的数量
    3. 如果数量增加，说明有新消息，通知主线程
    4. 使用Synchronize确保线程安全的主线程回调

  关键设计：
    - 忽略所有异常，确保监控线程持续运行
    - 第一次检查只记录基准数量，不触发通知
    - 使用Synchronize避免跨线程操作GUI的问题
}
procedure TSessionMonitorThread.Execute;
var
  SessionInfo: TSessionInfo;
  CurrentMessageCount: Integer;
  SessionFile: string;
begin
  while not Terminated do
  begin
    try
      if (FSessionDir <> '') and DirectoryExists(FSessionDir) then
      begin
        SessionFile := FSessionDir + '\session_context.json';
        if FileExists(SessionFile) then
        begin
          try
            SessionInfo := GetSessionInfo(FSessionDir);
            if SessionInfo.IsValid then
            begin
              CurrentMessageCount := SessionInfo.MessageCount;

              // 如果消息数量增加了，通知主线程
              if (FLastMessageCount > 0) and (CurrentMessageCount > FLastMessageCount) then
              begin
                FLastMessageCount := CurrentMessageCount;
                Synchronize(@NotifyNewMessage); // 线程安全的主线程回调
              end
              else if FLastMessageCount = 0 then
              begin
                // 第一次检查，只记录当前数量
                FLastMessageCount := CurrentMessageCount;
              end;
            end;
          except
            // 忽略读取错误，继续监控
          end;
        end;
      end;

      // 每2秒检查一次 - 平衡响应速度和系统资源
      Sleep(2000);

    except
      // 忽略所有异常，确保监控线程持续运行
    end;
  end;
end;

{
  TSessionMonitorThread.NotifyNewMessage - 线程安全的主线程通知

  功能：通知主线程检测到新消息

  关键设计：
    - 此方法通过Synchronize调用，确保在主线程中执行
    - 检查窗体引用有效性，避免访问已释放的对象
    - 调用主窗体的OnNewMessageDetected方法处理新消息
}
procedure TSessionMonitorThread.NotifyNewMessage;
begin
  if Assigned(FForm) then
  begin
    FForm.OnNewMessageDetected; // 在主线程中安全调用
  end;
end;

// ===== 主窗体方法实现 =====

procedure WriteLog(const msg: string);
var
  logFile: TextFile;
  timeStr: string;
begin
  try
    timeStr := FormatDateTime('yyyy-mm-dd hh:nn:ss', Now);
    AssignFile(logFile, 'gui_debug.log');
    if FileExists('gui_debug.log') then
      Append(logFile)
    else
      Rewrite(logFile);
    WriteLn(logFile, Format('[%s] %s', [timeStr, msg]));
    CloseFile(logFile);
  except
    // 忽略日志错误
  end;
end;



constructor TForm1.Create(AOwner: TComponent);
begin
  WriteLog('=== GUI 程序启动 ===');
  WriteLog('开始创建窗体');

  inherited Create(AOwner);

  WriteLog('设置窗体属性');
  Caption := 'RovoDev 对话界面 - 正在检查认证状态...';
  Width := 600;
  Height := 400;
  Position := poScreenCenter;

  // 初始化认证状态变量
  UserEmail := '';
  AuthStatus := '未知';
  IsAuthenticated := False;

  // 设置窗体显示事件
  OnShow := @FormShow;

  WriteLog('创建 GUI 控件');
  CreateGUI;

  WriteLog('创建初始化定时器');
  InitTimer := TTimer.Create(Self);
  InitTimer.Interval := 1000; // 1秒后执行
  InitTimer.Enabled := False;  // 先禁用
  InitTimer.OnTimer := @InitTimerTimer;

  WriteLog('初始化变量');
  ChatCount := 0;

  WriteLog('开始初始化会话');
  if InitSession then
  begin
    WriteLog('会话初始化成功');
    ShowStatus('✅ 初始化成功');
    AddChatMessage('系统', '欢迎！请输入问题开始对话。');
    AddChatMessage('系统', '配置信息:');
    AddChatMessage('系统', '- 配置文件: ' + ConfigFile);
    AddChatMessage('系统', '- 会话目录: ' + SessionDir);
    AddChatMessage('系统', '- 工作目录: ' + GetCurrentDir);
    WriteLog('界面初始化完成');
  end
  else
  begin
    WriteLog('会话初始化失败');
    ShowStatus('❌ 初始化失败');
    AddChatMessage('系统', '初始化失败信息:');
    AddChatMessage('系统', '- 配置文件: ' + ConfigFile);
    AddChatMessage('系统', '- 工作目录: ' + GetCurrentDir);
    SendButton.Enabled := False;
  end;

  WriteLog('启动自动认证检查定时器');
  InitTimer.Enabled := True; // 启动定时器进行自动认证检查

  WriteLog('=== GUI 程序启动完成 ===');
end;

procedure TForm1.CreateGUI;
begin
  WriteLog('开始创建 GUI 控件');

  // 聊天显示区
  WriteLog('创建聊天显示区');
  ChatMemo := TMemo.Create(Self);
  ChatMemo.Parent := Self;
  ChatMemo.Left := 10;
  ChatMemo.Top := 10;
  ChatMemo.Width := ClientWidth - 20;
  ChatMemo.Height := ClientHeight - 80;
  ChatMemo.Anchors := [akLeft, akTop, akRight, akBottom];
  ChatMemo.ReadOnly := True;
  ChatMemo.ScrollBars := ssVertical;
  // 设置字体支持 Unicode，尝试多种字体
  if Screen.Fonts.IndexOf('Segoe UI Emoji') >= 0 then
    ChatMemo.Font.Name := 'Segoe UI Emoji'
  else if Screen.Fonts.IndexOf('Microsoft YaHei') >= 0 then
    ChatMemo.Font.Name := 'Microsoft YaHei'
  else if Screen.Fonts.IndexOf('SimSun') >= 0 then
    ChatMemo.Font.Name := 'SimSun'
  else
    ChatMemo.Font.Name := 'Arial Unicode MS';
  ChatMemo.Font.Size := 10;

  // 输入框
  InputEdit := TEdit.Create(Self);
  InputEdit.Parent := Self;
  InputEdit.Left := 10;
  InputEdit.Top := ChatMemo.Top + ChatMemo.Height + 10;
  InputEdit.Width := ClientWidth - 145;  // 为两个按钮腾出空间
  InputEdit.Height := 25;
  InputEdit.Anchors := [akLeft, akBottom, akRight];
  InputEdit.OnKeyPress := @InputEditKeyPress;

  // 发送按钮
  SendButton := TButton.Create(Self);
  SendButton.Parent := Self;
  SendButton.Left := InputEdit.Left + InputEdit.Width + 10;
  SendButton.Top := InputEdit.Top;
  SendButton.Width := 60;
  SendButton.Height := 25;
  SendButton.Caption := '发送';
  SendButton.Anchors := [akRight, akBottom];
  SendButton.OnClick := @SendButtonClick;

  // 状态按钮
  StatusButton := TButton.Create(Self);
  StatusButton.Parent := Self;
  StatusButton.Left := SendButton.Left + SendButton.Width + 5;
  StatusButton.Top := InputEdit.Top;
  StatusButton.Width := 60;
  StatusButton.Height := 25;
  StatusButton.Caption := '状态';
  StatusButton.Anchors := [akRight, akBottom];
  StatusButton.OnClick := @StatusButtonClick;

  // 状态标签
  StatusLabel := TLabel.Create(Self);
  StatusLabel.Parent := Self;
  StatusLabel.Left := 10;
  StatusLabel.Top := InputEdit.Top + InputEdit.Height + 5;
  StatusLabel.Width := ClientWidth - 20;
  StatusLabel.Caption := '正在初始化...';
  StatusLabel.Anchors := [akLeft, akBottom, akRight];

  WriteLog('GUI 控件创建完成');
end;

function TForm1.InitSession: Boolean;
var
  SearchRec: TSearchRec;
  LatestTime: TDateTime;
  LatestDir: string;
  CurrentTime: TDateTime;
  SessionsDir: string;
begin
  WriteLog('开始初始化会话');
  Result := False;

  try
    WriteLog('设置配置文件路径');
    ConfigFile := 'custom_persistence_config.yml';

    WriteLog('检查配置文件: ' + ConfigFile);
    if not FileExists(ConfigFile) then
    begin
      WriteLog('配置文件不存在');
      ShowStatus('❌ 配置文件不存在');
      Exit;
    end;

    WriteLog('检查 acli.exe');
    if not FileExists('C:\test\acli.exe' ) then
    begin
      WriteLog('acli.exe 不存在');
      ShowStatus('❌ acli.exe 不存在');
      Exit;
    end;

    SessionsDir := 'custom_sessions';
    LatestTime := 0;
    LatestDir := '';

    if FindFirst(SessionsDir + '\*', faDirectory, SearchRec) = 0 then
    begin
      repeat
        if (SearchRec.Name <> '.') and (SearchRec.Name <> '..') and
           ((SearchRec.Attr and faDirectory) <> 0) and
           (Pos('_response_cache.txt', SearchRec.Name) = 0) then
        begin
          CurrentTime := FileDateToDateTime(SearchRec.Time);
          if CurrentTime > LatestTime then
          begin
            LatestTime := CurrentTime;
            LatestDir := SearchRec.Name;
          end;
        end;
      until FindNext(SearchRec) <> 0;
      FindClose(SearchRec);
    end;

    if LatestDir <> '' then
    begin
      SessionDir := GetCurrentDir + '\' + SessionsDir + '\' + LatestDir;
      Result := True;

      // 启动会话监控线程 - 异步对话系统的核心组件
      StartSessionMonitoring;
    end;

  except
    on E: Exception do
      ShowStatus('❌ 错误: ' + E.Message);
  end;
end;

function TForm1.CheckAuthStatus: string;
var
  cmdProcess: TProcess;
  OutputLines: TStringList;
  i: Integer;
  Line: string;
  ExitCode: Integer;
  WaitCount: Integer;
begin
  WriteLog('=== 开始检查认证状态 ===');
  Result := '';

  // 重置认证状态变量
  UserEmail := '';
  AuthStatus := '检查中...';
  IsAuthenticated := False;

  try
    // 创建 TProcess 来执行认证状态检查
    cmdProcess := TProcess.Create(nil);
    OutputLines := TStringList.Create;
    try
      WriteLog('设置 Process 参数');
      cmdProcess.Executable :=  'C:\test\acli.exe';
      cmdProcess.Parameters.Add('rovodev');
      cmdProcess.Parameters.Add('auth');
      cmdProcess.Parameters.Add('status');

      WriteLog('设置 Process 选项');
      cmdProcess.ShowWindow := swoHide;
      cmdProcess.Options := [poWaitOnExit, poNoConsole, poUsePipes];

      WriteLog('开始执行认证状态检查');


      WriteLog('命令：'+cmdProcess.CommandLine );
      cmdProcess.Execute;

      // 等待进程完成，最多等待 30 秒
      WaitCount := 0;
      while cmdProcess.Running and (WaitCount < 300) do  // 30秒 = 300 * 100ms
      begin
        Sleep(100);
        Application.ProcessMessages;
        Inc(WaitCount);
      end;

      if cmdProcess.Running then
      begin
        WriteLog('认证状态检查超时，强制终止');
        cmdProcess.Terminate(1);
        ExitCode := -2;
        Result := '❌ 检查超时';
        AuthStatus := '检查超时';
      end
      else
      begin
        ExitCode := cmdProcess.ExitStatus;
        WriteLog('认证状态检查完成，退出代码: ' + IntToStr(ExitCode));

        // 读取输出 - 使用更安全的方式
        try
          while cmdProcess.Output.NumBytesAvailable > 0 do
          begin
            SetLength(Line, cmdProcess.Output.NumBytesAvailable);
            cmdProcess.Output.Read(Line[1], Length(Line));
            OutputLines.Add(Line);
          end;

          WriteLog('获取到输出，行数: ' + IntToStr(OutputLines.Count));

          // 解析输出内容并提取用户信息
          for i := 0 to OutputLines.Count - 1 do
          begin
            Line := Trim(OutputLines[i]);
            WriteLog('输出行 ' + IntToStr(i) + ': ' + Line);
            if Line <> '' then
            begin
              if Result <> '' then
                Result := Result + #13#10;
              Result := Result + Line;

              // 解析认证状态
              if Pos('✓ Authenticated', Line) > 0 then
              begin
                IsAuthenticated := True;
                AuthStatus := '已认证';
              end
              else if (Pos('Email:', Line) > 0) or (Pos(' Email:', Line) > 0) then
              begin
                // 提取邮箱地址
                UserEmail := Trim(Copy(Line, Pos(':', Line) + 1, Length(Line)));
                WriteLog('提取到用户邮箱: ' + UserEmail);
              end;
            end;
          end;
        except
          on E: Exception do
          begin
            WriteLog('读取输出时出错: ' + E.Message);
            // 继续执行，不中断
          end;
        end;

        if Result = '' then
        begin
          if ExitCode = 0 then
          begin
            Result := '✅ 已认证 (无详细信息)';
            IsAuthenticated := True;
            AuthStatus := '已认证';
          end
          else
          begin
            Result := '❌ 未认证或认证失败 (退出代码: ' + IntToStr(ExitCode) + ')';
            IsAuthenticated := False;
            AuthStatus := '未认证';
          end;
        end
        else
        begin
          // 如果没有检测到认证状态，根据退出代码判断
          if not IsAuthenticated and (ExitCode <> 0) then
          begin
            IsAuthenticated := False;
            AuthStatus := '未认证';
          end;
        end;
      end;

    finally
      OutputLines.Free;
      cmdProcess.Free;
    end;

  except
    on E: Exception do
    begin
      WriteLog('检查认证状态时出错: ' + E.Message);
      Result := '❌ 检查失败: ' + E.Message;
      IsAuthenticated := False;
      AuthStatus := '检查失败';
    end;
  end;

  WriteLog('认证状态检查结果: ' + Result);
  WriteLog('用户邮箱: ' + UserEmail);
  WriteLog('认证状态: ' + AuthStatus);
  WriteLog('是否已认证: ' + BoolToStr(IsAuthenticated, True));

  // 更新窗体标题
  UpdateWindowTitle;

  WriteLog('=== 认证状态检查完成 ===');
end;

function TForm1.SendToAI(const question: string): string;
var
  Process: TProcess;
  ExitCode: Integer;
  WaitCount: Integer;
  AbsSessionDir: string;
  MessagePair: TMessagePair;
  SessionFile: string;
  SessionInfo: TSessionInfo;
begin
  WriteLog('进入 SendToAI 方法，问题: ' + question);
  Result := '';

  try
    WriteLog('显示发送状态');
    ShowStatus('⏳ 发送问题...');
    Application.ProcessMessages;

    {
      ===== 关键步骤：提前设置等待状态 =====

      这是解决竞态条件的核心：
      1. 在启动AI进程之前就设置IsWaitingForReply = True
      2. 记录当前消息数量作为基准
      3. 这样监控线程就能正确处理AI进程运行期间产生的新消息

      如果在进程完成后才设置等待状态，会导致：
      - AI进程快速完成并写入回复
      - 监控线程检测到新消息但IsWaitingForReply还是False
      - 新消息被忽略，用户看不到回复
    }
    WriteLog('设置等待回复状态（发送前）');
    try
      SessionInfo := GetSessionInfo(SessionDir);
      if SessionInfo.IsValid then
      begin
        PendingMessageCount := SessionInfo.MessageCount; // 记录基准消息数量
        IsWaitingForReply := True;                       // 设置等待标志
        WriteLog('设置等待回复状态（发送前），当前消息数量: ' + IntToStr(PendingMessageCount));
      end
      else
      begin
        WriteLog('❌ 无法获取会话信息');
        ShowStatus('❌ 无法获取会话信息');
        Result := '';
        Exit;
      end;
    except
      on E: Exception do
      begin
        WriteLog('设置等待状态时出错: ' + E.Message);
        ShowStatus('❌ 设置等待状态失败');
        Result := '';
        Exit;
      end;
    end;

    // 使用 TProcess 隐藏终端窗口
    WriteLog('创建 TProcess');
    Process := TProcess.Create(nil);
    try
      WriteLog('设置 Process 参数');
      Process.Executable := 'C:\test\acli.exe';
      Process.Parameters.Add('rovodev');
      Process.Parameters.Add('run');
      Process.Parameters.Add('--config-file');
      Process.Parameters.Add(ConfigFile);
      Process.Parameters.Add(question);

      WriteLog('设置 Process 选项');
      // 隐藏窗口并重定向输出
      Process.ShowWindow := swoHide;
      Process.Options := [poWaitOnExit, poNoConsole, poUsePipes];

      WriteLog('开始执行 Process (异步模式)');
      try
        // 使用异步模式，不等待进程完成
        Process.Options := [poNoConsole];


      WriteLog('命令：'+Process.CommandLine );
        Process.Execute;
        WriteLog('Process 启动成功，等待完成...');

        // 等待进程完成，最多等待 60 秒
        WaitCount := 0;
        while Process.Running and (WaitCount < 600) do  // 60秒 = 600 * 100ms
        begin
          Sleep(100);
          Application.ProcessMessages;
          Inc(WaitCount);
          if WaitCount mod 50 = 0 then  // 每5秒记录一次
            WriteLog('等待进程完成... ' + IntToStr(WaitCount div 10) + '秒');
        end;

        if Process.Running then
        begin
          WriteLog('进程超时，强制终止');
          Process.Terminate(1);
          ExitCode := -2;
        end
        else
        begin
          WriteLog('Process 执行完成');
          ExitCode := Process.ExitStatus;
          WriteLog('Process 退出代码: ' + IntToStr(ExitCode));
        end;
      except
        on E: Exception do
        begin
          WriteLog('Process 执行异常: ' + E.Message);
          ExitCode := -1;
        end;
      end;
    finally
      Process.Free;
    end;

    if ExitCode <> 0 then
    begin
      ShowStatus('❌ 发送失败，退出代码: ' + IntToStr(ExitCode));
      Exit;
    end;

    ShowStatus('⏳ 等待回复...');
    Application.ProcessMessages;

    {
      ===== 异步返回机制 =====

      返回'PENDING'表示：
      1. 消息已成功发送给AI
      2. 等待状态已设置，监控线程正在工作
      3. 主线程不需要等待，界面保持响应
      4. 监控线程检测到回复后会自动处理并显示
    }
    Result := 'PENDING'; // 表示正在等待回复，监控线程会处理
    WriteLog('SendToAI 返回: PENDING，等待监控线程处理回复');
  except
    on E: Exception do
    begin
      ShowStatus('❌ 错误: ' + E.Message);
      AddChatMessage('系统', '详细错误信息: ' + E.Message);
      AddChatMessage('系统', '错误类型: ' + E.ClassName);
    end;
  end;
end;



procedure TForm1.AddChatMessage(const speaker: string; const message: string);
var
  timeStr: string;
  prefix: string;
begin
  timeStr := FormatDateTime('hh:nn:ss', Now);

  case speaker of
    '系统': prefix := '🔧 系统';
    '您': prefix := '👤 您';
    'AI': prefix := '🤖 AI';
    else prefix := speaker;
  end;

  ChatMemo.Lines.Add(Format('[%s] %s: %s', [timeStr, prefix, message]));
  ChatMemo.Lines.Add('');

  // 滚动到底部
  ChatMemo.SelStart := Length(ChatMemo.Text);
  ChatMemo.SelLength := 0;
end;

procedure TForm1.ShowStatus(const status: string);
begin
  StatusLabel.Caption := status;
  Application.ProcessMessages;
end;

{
  ===== 会话监控相关方法 - 异步对话系统核心处理逻辑 =====
}

{
  OnNewMessageDetected - 处理监控线程检测到的新消息

  功能：当监控线程检测到会话文件中有新消息时，此方法被调用

  工作流程：
    1. 检查是否正在等待回复（IsWaitingForReply标志）
    2. 验证消息数量确实增加了
    3. 提取最新的AI回复
    4. 显示回复并重新启用界面
    5. 重置等待状态

  关键设计：
    - 只有在IsWaitingForReply=True时才处理新消息
    - 通过消息数量比较确认确实有新回复
    - 完整的错误处理确保系统稳定性
    - 处理完成后自动重置状态和重新启用界面
}
procedure TForm1.OnNewMessageDetected;
var
  SessionInfo: TSessionInfo;
  Response: string;
begin
  WriteLog('检测到新消息！IsWaitingForReply=' + BoolToStr(IsWaitingForReply, True) + ', PendingMessageCount=' + IntToStr(PendingMessageCount));

  // 如果正在等待回复
  if IsWaitingForReply then
  begin
    WriteLog('正在等待回复，开始处理新消息');
    try
      // 获取最新的回复
      SessionInfo := GetSessionInfo(SessionDir);
      WriteLog('获取会话信息：IsValid=' + BoolToStr(SessionInfo.IsValid, True) + ', MessageCount=' + IntToStr(SessionInfo.MessageCount));

      if SessionInfo.IsValid and (SessionInfo.MessageCount > PendingMessageCount) then
      begin
        WriteLog('消息数量从 ' + IntToStr(PendingMessageCount) + ' 增加到 ' + IntToStr(SessionInfo.MessageCount));

        // 提取最新的回复
        WriteLog('开始提取最新回复');
        Response := ExtractLatestResponseFromJSON(SessionDir + '\session_context.json');
        WriteLog('提取回复结果：长度=' + IntToStr(Length(Response)));

        if Response <> '' then
        begin
          WriteLog('✅ 成功提取回复，长度: ' + IntToStr(Length(Response)) + ' 字符');

          // 显示回复
          AddChatMessage('🤖 RovoDev', Response);
          ShowStatus('✅ 回复已接收');

          // 重置等待状态 - 重要：防止重复处理
          IsWaitingForReply := False;
          PendingMessageCount := 0;
          WriteLog('重置等待状态完成');

          // 重新启用界面 - 恢复用户交互
          SendButton.Enabled := True;
          InputEdit.Enabled := True;
          WriteLog('重新启用界面完成');
        end
        else
        begin
          WriteLog('❌ 提取回复为空');
        end;
      end
      else
      begin
        WriteLog('会话信息无效或消息数量未增加');
      end;
    except
      on E: Exception do
      begin
        WriteLog('处理新消息时出错: ' + E.Message);
        WriteLog('错误类型: ' + E.ClassName);
      end;
    end;
  end
  else
  begin
    WriteLog('当前不在等待回复状态，忽略新消息');
  end;
end;

{
  StartSessionMonitoring - 启动会话监控线程

  功能：创建并启动监控线程

  关键设计：
    - 先停止现有线程，避免重复创建
    - 只有在有效会话目录时才启动监控
    - 线程会立即开始监控会话文件变化
}
procedure TForm1.StartSessionMonitoring;
begin
  WriteLog('启动会话监控线程');

  // 停止现有的监控线程
  StopSessionMonitoring;

  // 创建新的监控线程
  if SessionDir <> '' then
  begin
    MonitorThread := TSessionMonitorThread.Create(Self, SessionDir);
    WriteLog('会话监控线程已启动，监控目录: ' + SessionDir);
  end;
end;

{
  StopSessionMonitoring - 停止会话监控线程

  功能：安全地终止监控线程

  关键设计：
    - 检查线程是否存在
    - 调用Terminate请求线程结束
    - 由于FreeOnTerminate=True，线程会自动释放
}
procedure TForm1.StopSessionMonitoring;
begin
  if Assigned(MonitorThread) then
  begin
    WriteLog('停止会话监控线程');
    MonitorThread.Terminate;
    MonitorThread := nil;
  end;
end;

procedure TForm1.SendButtonClick(Sender: TObject);
var
  question: string;
  response: string;
begin
  WriteLog('=== 开始发送问题 ===');
  question := Trim(InputEdit.Text);
  WriteLog('用户问题: ' + question);

  if question = '' then
  begin
    ShowMessage('请输入问题！');
    InputEdit.SetFocus;
    Exit;
  end;

  WriteLog('禁用按钮和输入框');
  SendButton.Enabled := False;
  InputEdit.Enabled := False;

  try
    Inc(ChatCount);
    WriteLog('聊天计数: ' + IntToStr(ChatCount));

    AddChatMessage('您', question);
    WriteLog('开始调用 SendToAI');

    response := SendToAI(question);
    WriteLog('SendToAI 返回: ' + response);

    if response = 'PENDING' then
    begin
      // 正在等待回复，界面保持禁用状态
      // 监控线程会在收到回复后重新启用界面
      InputEdit.Text := '';
      ShowStatus('⏳ 等待 RovoDev 回复...');
    end
    else if response <> '' then
    begin
      // 立即收到回复（不太可能发生）
      AddChatMessage('🤖 RovoDev', response);
      ShowStatus(Format('✅ 第 %d 轮对话完成', [ChatCount]));
      SendButton.Enabled := True;
      InputEdit.Enabled := True;
      InputEdit.Text := '';
      InputEdit.SetFocus;
    end
    else
    begin
      // 发送失败
      AddChatMessage('系统', '❌ 发送失败');
      Dec(ChatCount);
      SendButton.Enabled := True;
      InputEdit.Enabled := True;
      InputEdit.SetFocus;
    end;

  except
    on E: Exception do
    begin
      WriteLog('SendButtonClick 异常: ' + E.Message);
      AddChatMessage('系统', '❌ 发送异常: ' + E.Message);
      Dec(ChatCount);
      SendButton.Enabled := True;
      InputEdit.Enabled := True;
      InputEdit.SetFocus;
    end;
  end;
end;

procedure TForm1.StatusButtonClick(Sender: TObject);
var
  authResult: string;
begin
  WriteLog('=== 开始状态检查 ===');

  // 禁用按钮防止重复点击
  StatusButton.Enabled := False;
  SendButton.Enabled := False;

  try
    ShowStatus('⏳ 正在检查认证状态...');
    Application.ProcessMessages;

    AddChatMessage('系统', '正在检查 RovoDev 认证状态...');

    authResult := CheckAuthStatus;

    if authResult <> '' then
    begin
      AddChatMessage('系统', '认证状态检查结果:');
      AddChatMessage('系统', authResult);

      if Pos('✅', authResult) > 0 then
        ShowStatus('✅ 认证状态: 已认证')
      else if Pos('❌', authResult) > 0 then
        ShowStatus('❌ 认证状态: 未认证或失败')
      else
        ShowStatus('ℹ️ 认证状态: ' + Copy(authResult, 1, 30));
    end
    else
    begin
      AddChatMessage('系统', '❌ 无法获取认证状态');
      ShowStatus('❌ 状态检查失败');
    end;

  finally
    StatusButton.Enabled := True;
    SendButton.Enabled := True;
  end;

  WriteLog('=== 状态检查完成 ===');
end;

procedure TForm1.FormShow(Sender: TObject);
begin
  WriteLog('=== 窗体显示事件触发 ===');
  // 窗体显示后自动进行认证检查
  PerformInitialAuthCheck;
end;

{
  FormDestroy - 窗体销毁事件处理

  功能：程序退出时的清理工作

  关键设计：
    - 确保监控线程被正确终止
    - 避免内存泄漏和资源占用
}
procedure TForm1.FormDestroy(Sender: TObject);
begin
  WriteLog('=== 窗体销毁事件触发 ===');
  // 停止监控线程 - 重要：避免程序退出后线程仍在运行
  StopSessionMonitoring;
end;

procedure TForm1.InitTimerTimer(Sender: TObject);
begin
  WriteLog('=== 定时器触发，开始自动认证检查 ===');

  // 禁用定时器，只执行一次
  InitTimer.Enabled := False;

  // 执行自动认证检查
  PerformInitialAuthCheck;
end;

procedure TForm1.PerformInitialAuthCheck;
var
  authResult: string;
begin
  WriteLog('=== 开始自动认证检查 ===');

  try
    AddChatMessage('系统', '正在自动检查 RovoDev 认证状态...');
    ShowStatus('⏳ 检查认证状态...');
    Application.ProcessMessages;

    WriteLog('调用 CheckAuthStatus');
    authResult := CheckAuthStatus;
    WriteLog('CheckAuthStatus 返回: ' + authResult);

    if authResult <> '' then
    begin
      AddChatMessage('系统', '认证状态检查完成:');
      AddChatMessage('系统', authResult);

      if Pos('✓', authResult) > 0 then
      begin
        ShowStatus('✅ 已认证 - 可以开始对话');
        AddChatMessage('系统', '✅ 认证正常，您可以开始与 AI 对话了！');
        WriteLog('认证状态：已认证');
      end
      else if Pos('❌', authResult) > 0 then
      begin
        ShowStatus('❌ 未认证 - 请先登录');
        AddChatMessage('系统', '❌ 未认证，请先运行以下命令进行登录：');
        AddChatMessage('系统', '   acli rovodev auth login');
        AddChatMessage('系统', '或者点击"状态"按钮重新检查认证状态');
        WriteLog('认证状态：未认证');
      end
      else
      begin
        ShowStatus('ℹ️ 认证状态未知');
        AddChatMessage('系统', 'ℹ️ 认证状态信息不明确，建议点击"状态"按钮重新检查');
        WriteLog('认证状态：未知');
      end;
    end
    else
    begin
      ShowStatus('❌ 状态检查失败');
      AddChatMessage('系统', '❌ 无法获取认证状态，可能的原因：');
      AddChatMessage('系统', '- 网络连接问题');
      AddChatMessage('系统', '- acli.exe 不存在或无法执行');
      AddChatMessage('系统', '您可以点击"状态"按钮重新尝试检查');
      WriteLog('认证状态检查失败');
    end;

  except
    on E: Exception do
    begin
      ShowStatus('❌ 检查出错');
      AddChatMessage('系统', '❌ 认证状态检查出错: ' + E.Message);
      AddChatMessage('系统', '您可以点击"状态"按钮重新尝试检查');
      WriteLog('认证状态检查异常: ' + E.Message);
    end;
  end;

  AddChatMessage('系统', ''); // 添加空行分隔
  WriteLog('=== 自动认证检查完成 ===');
end;

procedure TForm1.UpdateWindowTitle;
var
  titleText: string;
begin
  WriteLog('=== 更新窗体标题 ===');

  titleText := 'RovoDev 对话界面';

  if IsAuthenticated then
  begin
    if UserEmail <> '' then
      titleText := titleText + ' - ✅ ' + UserEmail + ' (已认证)'
    else
      titleText := titleText + ' - ✅ 已认证';
  end
  else
  begin
    if AuthStatus <> '' then
      titleText := titleText + ' - ❌ ' + AuthStatus
    else
      titleText := titleText + ' - ❌ 未认证';
  end;

  Caption := titleText;
  WriteLog('窗体标题已更新: ' + titleText);
end;

procedure TForm1.InputEditKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // 回车发送
  begin
    Key := #0;
    SendButtonClick(nil);
  end;
end;

end.
