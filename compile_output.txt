Hint: (lazbuild) Primary config path: "C:\Users\<USER>\AppData\Local\lazarus"
InitializeFppkg failed: Could not find a fpc executable in the PATH
Hint: (lazarus) [TBuildManager.SetBuildTarget] Old=x86_64-win64--win32 New=x86_64-win64--win32 Changed: OS/CPU=True LCL=False
Hint: (lazarus) [TBuildManager.SetBuildTarget] Old=x86_64-win64--win32 New=x86_64-win64--win32 Changed: OS/CPU=True LCL=False
Hint: (lazarus) Project needs building: Compile was incomplete for Project
Info: (lazarus) Execute Title="编译工程,目标:C:\test\chatsessiontest\testproject\testproject1.exe"
Info: (lazarus) Working Directory="C:\test\chatsessiontest\testproject\"
Info: (lazarus) Executable="C:\lazarus\fpc\3.2.2\bin\x86_64-win64\fpc.exe"
Info: (lazarus) Param[0]="-MObjFPC"
Info: (lazarus) Param[1]="-Scghi"
Info: (lazarus) Param[2]="-O1"
Info: (lazarus) Param[3]="-gw3"
Info: (lazarus) Param[4]="-gl"
Info: (lazarus) Param[5]="-WG"
Info: (lazarus) Param[6]="-l"
Info: (lazarus) Param[7]="-vewnhibq"
Info: (lazarus) Param[8]="-FiC:\test\chatsessiontest\testproject\lib\x86_64-win64"
Info: (lazarus) Param[9]="-FuC:\test\chatsessiontest\testproject\"
Info: (lazarus) Param[10]="-FuC:\lazarus\lcl\units\x86_64-win64\win32"
Info: (lazarus) Param[11]="-FuC:\lazarus\lcl\units\x86_64-win64"
Info: (lazarus) Param[12]="-FuC:\lazarus\components\freetype\lib\x86_64-win64"
Info: (lazarus) Param[13]="-FuC:\lazarus\components\lazutils\lib\x86_64-win64"
Info: (lazarus) Param[14]="-FuC:\lazarus\packager\units\x86_64-win64"
Info: (lazarus) Param[15]="-FUC:\test\chatsessiontest\testproject\lib\x86_64-win64\"
Info: (lazarus) Param[16]="-FEC:\test\chatsessiontest\testproject\"
Info: (lazarus) Param[17]="-oC:\test\chatsessiontest\testproject\testproject1.exe"
Info: (lazarus) Param[18]="-dLCL"
Info: (lazarus) Param[19]="-dLCLwin32"
Info: (lazarus) Param[20]="testproject1.lpr"
Hint: (11030) Start of reading config file C:\lazarus\fpc\3.2.2\bin\x86_64-win64\fpc.cfg
Hint: (11031) End of reading config file C:\lazarus\fpc\3.2.2\bin\x86_64-win64\fpc.cfg
Free Pascal Compiler version 3.2.2 [2025/05/03] for x86_64
Copyright (c) 1993-2021 by Florian Klaempfl and others
(1002) Target OS: Win64 for x64
(3104) Compiling testproject1.lpr
(3104) Compiling testunit1.pas
(3104) Compiling SessionUtils.pas
C:\test\chatsessiontest\testproject\SessionUtils.pas(78,88) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(78,111) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(79,64) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(79,88) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(91,69) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(91,101) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(91,111) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(95,60) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(95,83) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(95,97) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(100,72) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(100,82) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(108,81) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(108,123) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(109,58) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(109,82) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(110,64) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(110,89) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(129,93) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(129,101) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(134,64) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(134,72) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(142,70) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(142,78) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(145,85) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(145,111) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(156,72) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(156,80) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(157,66) Warning: (4104) Implicit string type conversion from "ShortString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(157,76) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(177,68) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(177,98) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(185,63) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(185,90) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(185,101) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(199,86) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(199,111) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(202,93) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(202,117) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(219,86) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(219,94) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(220,62) Warning: (4104) Implicit string type conversion from "ShortString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(220,72) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(183,31) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(194,38) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(194,38) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(196,30) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(230,9) Note: (5025) Local variable "pos2" not used
C:\test\chatsessiontest\testproject\SessionUtils.pas(370,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(370,61) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(371,44) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(371,62) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(372,44) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(372,78) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(373,44) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(373,82) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\SessionUtils.pas(302,3) Note: (5025) Local variable "logMsg" not used
C:\test\chatsessiontest\testproject\SessionUtils.pas(441,31) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(450,38) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(450,38) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(452,30) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(461,42) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(461,42) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(467,48) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(467,48) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(469,40) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(511,3) Note: (5027) Local variable "FoundQuestion" is assigned but never used
C:\test\chatsessiontest\testproject\SessionUtils.pas(541,31) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(550,38) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(550,38) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(552,30) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(560,42) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(560,42) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(566,48) Note: (6058) Call to subroutine "operator =(const op1:Variant;const op2:Variant):Boolean;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(566,48) Note: (6058) Call to subroutine "operator :=(const source:ShortString):Variant;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\SessionUtils.pas(568,40) Note: (6058) Call to subroutine "operator :=(const source:Variant):AnsiString;" marked as inline is not inlined
C:\test\chatsessiontest\testproject\testunit1.pas(119,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(119,61) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(120,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(120,61) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(121,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(121,64) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(129,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(129,61) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(130,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(130,64) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(225,39) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(225,49) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(271,36) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(271,44) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(310,39) Warning: (5043) Symbol "CommandLine" is deprecated
C:\test\chatsessiontest\testproject\testunit1.pas(310,38) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(310,51) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(333,64) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(333,82) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(339,27) Hint: (5091) Local variable "Line" of a managed type does not seem to be initialized
C:\test\chatsessiontest\testproject\testunit1.pas(344,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(344,78) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(350,37) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(350,58) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(350,62) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(367,54) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(367,63) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(374,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(374,59) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(389,72) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(389,91) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(413,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(413,59) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(414,41) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(414,38) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(420,43) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(420,49) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(421,31) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(421,40) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(422,31) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(422,41) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(423,34) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(423,66) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(441,50) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(441,58) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(472,36) Warning: (5043) Symbol "CommandLine" is deprecated
C:\test\chatsessiontest\testproject\testunit1.pas(472,35) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(472,48) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(484,49) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(484,83) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(497,47) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(497,65) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(502,48) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(502,56) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(512,56) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(512,74) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(534,36) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(534,44) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(535,58) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(535,66) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(536,52) Warning: (4104) Implicit string type conversion from "ShortString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(536,62) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(436,3) Note: (5025) Local variable "AbsSessionDir" not used
C:\test\chatsessiontest\testproject\testunit1.pas(437,3) Note: (5025) Local variable "MessagePair" not used
C:\test\chatsessiontest\testproject\testunit1.pas(438,3) Note: (5025) Local variable "SessionFile" not used
C:\test\chatsessiontest\testproject\testunit1.pas(439,3) Note: (5025) Local variable "SessionInfo" not used
C:\test\chatsessiontest\testproject\testunit1.pas(543,13) Error: (5002) Duplicate identifier "SessionDir"
C:\test\chatsessiontest\testproject\testunit1.pas(543,13) Hint: (5003) Identifier already defined in testunit1.pas at line 22
C:\test\chatsessiontest\testproject\testunit1.pas(556,19) Error: (5000) Identifier not found "FindLatestSessionDir"
C:\test\chatsessiontest\testproject\testunit1.pas(556,40) Error: (5000) Identifier not found "SessionsDir"
C:\test\chatsessiontest\testproject\testunit1.pas(567,48) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(567,77) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(571,60) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(571,68) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(588,44) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(588,73) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(588,98) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(591,21) Error: (5000) Identifier not found "FindLatestSessionDir"
C:\test\chatsessiontest\testproject\testunit1.pas(591,42) Error: (5000) Identifier not found "SessionsDir"
C:\test\chatsessiontest\testproject\testunit1.pas(601,34) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(601,63) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(602,47) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(603,41) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(603,70) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(615,66) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(615,102) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(625,58) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(625,66) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(632,60) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(632,68) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(638,52) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(638,63) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(656,49) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(656,57) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(657,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(657,59) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(661,59) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(661,83) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(699,31) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(699,39) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(714,33) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(714,52) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(720,51) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(720,77) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(769,46) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(769,69) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(816,43) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(816,53) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(858,68) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(858,76) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(860,48) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(860,56) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(879,20) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(879,44) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(879,54) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(881,20) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(881,30) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(886,20) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(886,44) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(886,42) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(888,20) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(888,30) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
C:\test\chatsessiontest\testproject\testunit1.pas(892,40) Warning: (4104) Implicit string type conversion from "AnsiString" to "UnicodeString"
C:\test\chatsessiontest\testproject\testunit1.pas(892,49) Warning: (4105) Implicit string type conversion with potential data loss from "UnicodeString" to "AnsiString"
testunit1.pas(905) Fatal: (10026) There were 5 errors compiling module, stopping
Fatal: (1018) Compilation aborted
Error: C:\lazarus\fpc\3.2.2\bin\x86_64-win64\ppcx64.exe returned an error exitcode
Error: (lazarus) 编译工程,目标:C:\test\chatsessiontest\testproject\testproject1.exe: stopped with exit code 1
Info: (lazarus) [TCompiler.Compile] end
Error: (lazbuild) Failed compiling of project "C:\test\chatsessiontest\testproject\testproject1.lpi"
