program DebugTest;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils;

begin
  WriteLn('=== 调试测试程序 ===');
  WriteLn('当前时间: ', FormatDateTime('yyyy-mm-dd hh:nn:ss', Now));
  WriteLn('当前目录: ', GetCurrentDir);
  WriteLn('程序启动成功！');
  
  // 测试文件操作
  if FileExists('custom_persistence_config.yml') then
    WriteLn('✅ 配置文件存在')
  else
    WriteLn('❌ 配置文件不存在');
    
  if FileExists('C:\test\acli.exe') then
    WriteLn('✅ acli.exe 存在')
  else
    WriteLn('❌ acli.exe 不存在');
    
  WriteLn('=== 测试完成 ===');
  WriteLn('按回车键退出...');
  ReadLn;
end.
