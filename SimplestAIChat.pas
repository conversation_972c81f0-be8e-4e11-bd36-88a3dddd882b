program SimplestAIChat;

{$mode objfpc}{$H+}

{
  ===============================================================================
  最简单的异步AI对话示例 - 一行代码搞定！
  ===============================================================================
  
  这个示例展示了如何用最简单的方式使用 AsyncAIChat 组件
  
  特点：
    - 只需要一行代码发送消息
    - 自动处理所有异步逻辑
    - 可选的回调函数
    - 自动资源管理
  
  ===============================================================================
}

uses
  Classes, SysUtils, AsyncAIChat;

// 回调函数示例
procedure OnAIResponse(const Response: string);
begin
  WriteLn('');
  WriteLn('🤖 AI回复: ', Response);
  WriteLn('');
  WriteLn('对话完成！输入新消息继续，或输入 "quit" 退出');
  Write('> ');
end;

procedure OnAIError(const ErrorMsg: string);
begin
  WriteLn('');
  WriteLn('❌ 错误: ', ErrorMsg);
  WriteLn('');
  Write('> ');
end;

procedure OnAIStatus(const Status: string);
begin
  WriteLn('[状态] ', Status);
end;

var
  UserInput: string;

begin
  WriteLn('===============================================================================');
  WriteLn('                    超简单异步AI对话 - 一行代码版本');
  WriteLn('===============================================================================');
  WriteLn('');
  WriteLn('使用说明：');
  WriteLn('  - 输入消息并按回车发送给AI');
  WriteLn('  - 消息会异步发送，不会阻塞界面');
  WriteLn('  - 输入 "quit" 退出程序');
  WriteLn('');
  WriteLn('开始对话：');
  
  repeat
    Write('> ');
    ReadLn(UserInput);
    UserInput := Trim(UserInput);
    
    if UserInput = '' then
      Continue;
      
    if LowerCase(UserInput) = 'quit' then
      Break;
    
    // ===== 这就是全部！一行代码搞定异步AI对话！ =====
    if SendAIMessage(UserInput, @OnAIResponse, @OnAIError, @OnAIStatus) then
      WriteLn('消息已发送，等待AI回复...')
    else
      WriteLn('消息发送失败');
    
  until False;
  
  WriteLn('');
  WriteLn('正在退出...');
  StopAIChat; // 清理资源（可选，程序退出时会自动调用）
  WriteLn('再见！');
end.

{
  ===============================================================================
  使用说明
  ===============================================================================
  
  1. 最简单的调用（使用默认配置）：
     SendAIMessage('你好');
  
  2. 带回调的调用：
     SendAIMessage('你好', @OnResponse, @OnError);
  
  3. 完整参数调用：
     SendAIMessage('你好', @OnResponse, @OnError, @OnStatus, 
                   'C:\path\to\ai.exe', 'config.yml', 'sessions\session-id');
  
  4. 程序退出时（可选）：
     StopAIChat;
  
  特点：
    - 自动创建和管理 TAsyncAIChat 实例
    - 自动启动监控线程
    - 自动配置默认参数
    - 自动资源清理
    - 支持多次调用，共享同一个实例
  
  ===============================================================================
}
