[2025-07-16 21:20:01] === GUI 程序启动 ===
[2025-07-16 21:20:01] 开始创建窗体
[2025-07-16 21:20:01] 设置窗体属性
[2025-07-16 21:20:01] 创建 GUI 控件
[2025-07-16 21:20:01] 开始创建 GUI 控件
[2025-07-16 21:20:01] 创建聊天显示区
[2025-07-16 21:20:01] GUI 控件创建完成
[2025-07-16 21:20:01] 创建初始化定时器
[2025-07-16 21:20:01] 初始化变量
[2025-07-16 21:20:01] 开始初始化会话
[2025-07-16 21:20:01] 开始初始化会话
[2025-07-16 21:20:01] 设置配置文件路径
[2025-07-16 21:20:01] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:20:01] 配置文件不存在
[2025-07-16 21:20:01] 会话初始化失败
[2025-07-16 21:20:01] 启动自动认证检查定时器
[2025-07-16 21:20:01] === GUI 程序启动完成 ===
[2025-07-16 21:20:01] === 窗体显示事件触发 ===
[2025-07-16 21:20:01] === 开始自动认证检查 ===
[2025-07-16 21:20:01] 调用 CheckAuthStatus
[2025-07-16 21:20:01] === 开始检查认证状态 ===
[2025-07-16 21:20:01] 设置 Process 参数
[2025-07-16 21:20:01] 设置 Process 选项
[2025-07-16 21:20:01] 开始执行认证状态检查
[2025-07-16 21:20:04] 检查认证状态时出错: Failed to execute  : 2
[2025-07-16 21:20:04] 认证状态检查结果: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:04] 用户邮箱: 
[2025-07-16 21:20:04] 认证状态: 检查失败
[2025-07-16 21:20:04] 是否已认证: False
[2025-07-16 21:20:04] === 更新窗体标题 ===
[2025-07-16 21:20:04] 窗体标题已更新: RovoDev 对话界面 - ❌ 检查失败
[2025-07-16 21:20:04] === 认证状态检查完成 ===
[2025-07-16 21:20:04] CheckAuthStatus 返回: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:04] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:20:04] === 开始自动认证检查 ===
[2025-07-16 21:20:04] 调用 CheckAuthStatus
[2025-07-16 21:20:04] === 开始检查认证状态 ===
[2025-07-16 21:20:04] 设置 Process 参数
[2025-07-16 21:20:04] 设置 Process 选项
[2025-07-16 21:20:04] 开始执行认证状态检查
[2025-07-16 21:20:06] 检查认证状态时出错: Failed to execute  : 2
[2025-07-16 21:20:06] 认证状态检查结果: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:06] 用户邮箱: 
[2025-07-16 21:20:06] 认证状态: 检查失败
[2025-07-16 21:20:06] 是否已认证: False
[2025-07-16 21:20:06] === 更新窗体标题 ===
[2025-07-16 21:20:06] 窗体标题已更新: RovoDev 对话界面 - ❌ 检查失败
[2025-07-16 21:20:06] === 认证状态检查完成 ===
[2025-07-16 21:20:06] CheckAuthStatus 返回: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:06] 认证状态：未认证
[2025-07-16 21:20:06] === 自动认证检查完成 ===
[2025-07-16 21:20:06] 认证状态：未认证
[2025-07-16 21:20:06] === 自动认证检查完成 ===
[2025-07-16 21:25:45] === GUI 程序启动 ===
[2025-07-16 21:25:45] 开始创建窗体
[2025-07-16 21:25:45] 设置窗体属性
[2025-07-16 21:25:45] 创建 GUI 控件
[2025-07-16 21:25:45] 开始创建 GUI 控件
[2025-07-16 21:25:45] 创建聊天显示区
[2025-07-16 21:25:45] GUI 控件创建完成
[2025-07-16 21:25:45] 创建初始化定时器
[2025-07-16 21:25:45] 初始化变量
[2025-07-16 21:25:45] 开始初始化会话
[2025-07-16 21:25:45] 开始初始化会话
[2025-07-16 21:25:45] 设置配置文件路径
[2025-07-16 21:25:45] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:25:45] 检查 acli.exe
[2025-07-16 21:25:45] 会话初始化失败
[2025-07-16 21:25:45] 启动自动认证检查定时器
[2025-07-16 21:25:45] === GUI 程序启动完成 ===
[2025-07-16 21:25:45] === 窗体显示事件触发 ===
[2025-07-16 21:25:45] === 开始自动认证检查 ===
[2025-07-16 21:25:45] 调用 CheckAuthStatus
[2025-07-16 21:25:45] === 开始检查认证状态 ===
[2025-07-16 21:25:45] 设置 Process 参数
[2025-07-16 21:25:45] 设置 Process 选项
[2025-07-16 21:25:45] 开始执行认证状态检查
[2025-07-16 21:25:47] 认证状态检查完成，退出代码: 0
[2025-07-16 21:25:47] 获取到输出，行数: 1
[2025-07-16 21:25:47] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:47] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:47] 用户邮箱: 
[2025-07-16 21:25:47] 认证状态: 已认证
[2025-07-16 21:25:47] 是否已认证: True
[2025-07-16 21:25:47] === 更新窗体标题 ===
[2025-07-16 21:25:47] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:25:47] === 认证状态检查完成 ===
[2025-07-16 21:25:47] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:47] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:25:47] === 开始自动认证检查 ===
[2025-07-16 21:25:47] 调用 CheckAuthStatus
[2025-07-16 21:25:47] === 开始检查认证状态 ===
[2025-07-16 21:25:47] 设置 Process 参数
[2025-07-16 21:25:47] 设置 Process 选项
[2025-07-16 21:25:47] 开始执行认证状态检查
[2025-07-16 21:25:49] 认证状态检查完成，退出代码: 0
[2025-07-16 21:25:49] 获取到输出，行数: 1
[2025-07-16 21:25:49] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:49] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:49] 用户邮箱: 
[2025-07-16 21:25:49] 认证状态: 已认证
[2025-07-16 21:25:49] 是否已认证: True
[2025-07-16 21:25:49] === 更新窗体标题 ===
[2025-07-16 21:25:49] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:25:49] === 认证状态检查完成 ===
[2025-07-16 21:25:49] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:49] 认证状态：已认证
[2025-07-16 21:25:49] === 自动认证检查完成 ===
[2025-07-16 21:25:49] 认证状态：已认证
[2025-07-16 21:25:49] === 自动认证检查完成 ===
[2025-07-16 21:26:06] === 开始状态检查 ===
[2025-07-16 21:26:06] === 开始检查认证状态 ===
[2025-07-16 21:26:06] 设置 Process 参数
[2025-07-16 21:26:06] 设置 Process 选项
[2025-07-16 21:26:06] 开始执行认证状态检查
[2025-07-16 21:26:08] 认证状态检查完成，退出代码: 0
[2025-07-16 21:26:08] 获取到输出，行数: 1
[2025-07-16 21:26:08] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:26:08] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:26:08] 用户邮箱: 
[2025-07-16 21:26:08] 认证状态: 已认证
[2025-07-16 21:26:08] 是否已认证: True
[2025-07-16 21:26:08] === 更新窗体标题 ===
[2025-07-16 21:26:08] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:26:08] === 认证状态检查完成 ===
[2025-07-16 21:26:08] === 状态检查完成 ===
[2025-07-16 21:26:10] === 开始发送问题 ===
[2025-07-16 21:26:10] 用户问题: 测试
[2025-07-16 21:26:10] 禁用按钮和输入框
[2025-07-16 21:26:10] 聊天计数: 1
[2025-07-16 21:26:10] 开始调用 SendToAI
[2025-07-16 21:26:10] 进入 SendToAI 方法，问题: 测试
[2025-07-16 21:26:10] 显示发送状态
[2025-07-16 21:26:10] 创建 TProcess
[2025-07-16 21:26:10] 设置 Process 参数
[2025-07-16 21:26:10] 设置 Process 选项
[2025-07-16 21:26:10] 开始执行 Process (异步模式)
[2025-07-16 21:26:10] Process 启动成功，等待完成...
[2025-07-16 21:26:16] 等待进程完成... 5秒
[2025-07-16 21:26:22] 等待进程完成... 10秒
[2025-07-16 21:26:27] 等待进程完成... 15秒
[2025-07-16 21:26:33] 等待进程完成... 20秒
[2025-07-16 21:26:38] 等待进程完成... 25秒
[2025-07-16 21:26:44] 等待进程完成... 30秒
[2025-07-16 21:26:49] 等待进程完成... 35秒
[2025-07-16 21:26:55] 等待进程完成... 40秒
[2025-07-16 21:27:00] 等待进程完成... 45秒
[2025-07-16 21:27:06] 等待进程完成... 50秒
[2025-07-16 21:27:11] 等待进程完成... 55秒
[2025-07-16 21:27:17] 等待进程完成... 60秒
[2025-07-16 21:27:17] 进程超时，强制终止
[2025-07-16 21:27:17] SendToAI 返回，回复长度: 0
[2025-07-16 21:30:10] === GUI 程序启动 ===
[2025-07-16 21:30:10] 开始创建窗体
[2025-07-16 21:30:10] 设置窗体属性
[2025-07-16 21:30:10] 创建 GUI 控件
[2025-07-16 21:30:10] 开始创建 GUI 控件
[2025-07-16 21:30:10] 创建聊天显示区
[2025-07-16 21:30:10] GUI 控件创建完成
[2025-07-16 21:30:10] 创建初始化定时器
[2025-07-16 21:30:10] 初始化变量
[2025-07-16 21:30:10] 开始初始化会话
[2025-07-16 21:30:10] 开始初始化会话
[2025-07-16 21:30:10] 设置配置文件路径
[2025-07-16 21:30:10] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:30:10] 检查 acli.exe
[2025-07-16 21:30:10] 会话初始化失败
[2025-07-16 21:30:10] 启动自动认证检查定时器
[2025-07-16 21:30:10] === GUI 程序启动完成 ===
[2025-07-16 21:30:10] === 窗体显示事件触发 ===
[2025-07-16 21:30:10] === 开始自动认证检查 ===
[2025-07-16 21:30:10] 调用 CheckAuthStatus
[2025-07-16 21:30:10] === 开始检查认证状态 ===
[2025-07-16 21:30:10] 设置 Process 参数
[2025-07-16 21:30:10] 设置 Process 选项
[2025-07-16 21:30:10] 开始执行认证状态检查
[2025-07-16 21:30:12] 认证状态检查完成，退出代码: 0
[2025-07-16 21:30:12] 获取到输出，行数: 1
[2025-07-16 21:30:12] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:12] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:12] 用户邮箱: 
[2025-07-16 21:30:12] 认证状态: 已认证
[2025-07-16 21:30:12] 是否已认证: True
[2025-07-16 21:30:12] === 更新窗体标题 ===
[2025-07-16 21:30:12] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:30:12] === 认证状态检查完成 ===
[2025-07-16 21:30:12] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:12] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:30:12] === 开始自动认证检查 ===
[2025-07-16 21:30:12] 调用 CheckAuthStatus
[2025-07-16 21:30:12] === 开始检查认证状态 ===
[2025-07-16 21:30:12] 设置 Process 参数
[2025-07-16 21:30:12] 设置 Process 选项
[2025-07-16 21:30:12] 开始执行认证状态检查
[2025-07-16 21:30:14] 认证状态检查完成，退出代码: 0
[2025-07-16 21:30:14] 获取到输出，行数: 1
[2025-07-16 21:30:14] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:14] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:14] 用户邮箱: 
[2025-07-16 21:30:14] 认证状态: 已认证
[2025-07-16 21:30:14] 是否已认证: True
[2025-07-16 21:30:14] === 更新窗体标题 ===
[2025-07-16 21:30:14] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:30:14] === 认证状态检查完成 ===
[2025-07-16 21:30:14] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:14] 认证状态：已认证
[2025-07-16 21:30:14] === 自动认证检查完成 ===
[2025-07-16 21:30:14] 认证状态：已认证
[2025-07-16 21:30:14] === 自动认证检查完成 ===
[2025-07-16 21:31:58] === GUI 程序启动 ===
[2025-07-16 21:31:58] 开始创建窗体
[2025-07-16 21:31:58] 设置窗体属性
[2025-07-16 21:31:58] 创建 GUI 控件
[2025-07-16 21:31:58] 开始创建 GUI 控件
[2025-07-16 21:31:58] 创建聊天显示区
[2025-07-16 21:31:58] GUI 控件创建完成
[2025-07-16 21:31:58] 创建初始化定时器
[2025-07-16 21:31:58] 初始化变量
[2025-07-16 21:31:58] 开始初始化会话
[2025-07-16 21:31:58] 开始初始化会话
[2025-07-16 21:31:58] 设置配置文件路径
[2025-07-16 21:31:58] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:31:58] 检查 acli.exe
[2025-07-16 21:31:58] 会话初始化失败
[2025-07-16 21:31:58] 启动自动认证检查定时器
[2025-07-16 21:31:58] === GUI 程序启动完成 ===
[2025-07-16 21:31:58] === 窗体显示事件触发 ===
[2025-07-16 21:31:58] === 开始自动认证检查 ===
[2025-07-16 21:31:58] 调用 CheckAuthStatus
[2025-07-16 21:31:58] === 开始检查认证状态 ===
[2025-07-16 21:31:58] 设置 Process 参数
[2025-07-16 21:31:58] 设置 Process 选项
[2025-07-16 21:31:58] 开始执行认证状态检查
[2025-07-16 21:32:00] 认证状态检查完成，退出代码: 0
[2025-07-16 21:32:00] 获取到输出，行数: 1
[2025-07-16 21:32:00] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:00] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:00] 用户邮箱: 
[2025-07-16 21:32:00] 认证状态: 已认证
[2025-07-16 21:32:00] 是否已认证: True
[2025-07-16 21:32:00] === 更新窗体标题 ===
[2025-07-16 21:32:00] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:32:00] === 认证状态检查完成 ===
[2025-07-16 21:32:00] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:00] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:32:00] === 开始自动认证检查 ===
[2025-07-16 21:32:00] 调用 CheckAuthStatus
[2025-07-16 21:32:00] === 开始检查认证状态 ===
[2025-07-16 21:32:00] 设置 Process 参数
[2025-07-16 21:32:00] 设置 Process 选项
[2025-07-16 21:32:00] 开始执行认证状态检查
[2025-07-16 21:32:02] 认证状态检查完成，退出代码: 0
[2025-07-16 21:32:02] 获取到输出，行数: 1
[2025-07-16 21:32:02] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:02] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:02] 用户邮箱: 
[2025-07-16 21:32:02] 认证状态: 已认证
[2025-07-16 21:32:02] 是否已认证: True
[2025-07-16 21:32:02] === 更新窗体标题 ===
[2025-07-16 21:32:02] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:32:02] === 认证状态检查完成 ===
[2025-07-16 21:32:02] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:02] 认证状态：已认证
[2025-07-16 21:32:02] === 自动认证检查完成 ===
[2025-07-16 21:32:02] 认证状态：已认证
[2025-07-16 21:32:02] === 自动认证检查完成 ===
[2025-07-16 21:32:14] === 开始发送问题 ===
[2025-07-16 21:32:14] 用户问题: 你好
[2025-07-16 21:32:14] 禁用按钮和输入框
[2025-07-16 21:32:14] 聊天计数: 1
[2025-07-16 21:32:14] 开始调用 SendToAI
[2025-07-16 21:32:14] 进入 SendToAI 方法，问题: 你好
[2025-07-16 21:32:14] 显示发送状态
[2025-07-16 21:32:14] 创建 TProcess
[2025-07-16 21:32:14] 设置 Process 参数
[2025-07-16 21:32:14] 设置 Process 选项
[2025-07-16 21:32:14] 开始执行 Process (异步模式)
[2025-07-16 21:32:14] Process 启动成功，等待完成...
[2025-07-16 21:32:19] 等待进程完成... 5秒
[2025-07-16 21:32:25] 等待进程完成... 10秒
[2025-07-16 21:32:30] 等待进程完成... 15秒
[2025-07-16 21:32:36] 等待进程完成... 20秒
[2025-07-16 21:32:42] 等待进程完成... 25秒
[2025-07-16 21:32:47] 等待进程完成... 30秒
[2025-07-16 21:32:51] Process 执行完成
[2025-07-16 21:32:51] Process 退出代码: 0
[2025-07-16 21:32:57] 开始逐步测试 SessionUtils 函数
[2025-07-16 21:32:57] 会话目录: 
[2025-07-16 21:32:57] 步骤1：检查会话文件: \session_context.json
[2025-07-16 21:32:57] ❌ 会话文件不存在
[2025-07-16 21:32:57] SendToAI 返回，回复长度: 0
[2025-07-16 21:37:36] === GUI 程序启动 ===
[2025-07-16 21:37:36] 开始创建窗体
[2025-07-16 21:37:36] 设置窗体属性
[2025-07-16 21:37:36] 创建 GUI 控件
[2025-07-16 21:37:36] 开始创建 GUI 控件
[2025-07-16 21:37:36] 创建聊天显示区
[2025-07-16 21:37:36] GUI 控件创建完成
[2025-07-16 21:37:36] 创建初始化定时器
[2025-07-16 21:37:36] 初始化变量
[2025-07-16 21:37:36] 开始初始化会话
[2025-07-16 21:37:36] 开始初始化会话
[2025-07-16 21:37:36] 设置配置文件路径
[2025-07-16 21:37:36] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:37:36] 检查 acli.exe
[2025-07-16 21:37:36] 会话初始化成功
[2025-07-16 21:37:36] 界面初始化完成
[2025-07-16 21:37:36] 启动自动认证检查定时器
[2025-07-16 21:37:36] === GUI 程序启动完成 ===
[2025-07-16 21:37:36] === 窗体显示事件触发 ===
[2025-07-16 21:37:36] === 开始自动认证检查 ===
[2025-07-16 21:37:36] 调用 CheckAuthStatus
[2025-07-16 21:37:36] === 开始检查认证状态 ===
[2025-07-16 21:37:36] 设置 Process 参数
[2025-07-16 21:37:36] 设置 Process 选项
[2025-07-16 21:37:36] 开始执行认证状态检查
[2025-07-16 21:37:36] 
[2025-07-16 21:37:38] 认证状态检查完成，退出代码: 0
[2025-07-16 21:37:38] 获取到输出，行数: 1
[2025-07-16 21:37:38] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:38] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:38] 用户邮箱: 
[2025-07-16 21:37:38] 认证状态: 已认证
[2025-07-16 21:37:38] 是否已认证: True
[2025-07-16 21:37:38] === 更新窗体标题 ===
[2025-07-16 21:37:38] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:37:38] === 认证状态检查完成 ===
[2025-07-16 21:37:38] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:38] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:37:38] === 开始自动认证检查 ===
[2025-07-16 21:37:38] 调用 CheckAuthStatus
[2025-07-16 21:37:38] === 开始检查认证状态 ===
[2025-07-16 21:37:38] 设置 Process 参数
[2025-07-16 21:37:38] 设置 Process 选项
[2025-07-16 21:37:38] 开始执行认证状态检查
[2025-07-16 21:37:38] 
[2025-07-16 21:37:40] 认证状态检查完成，退出代码: 0
[2025-07-16 21:37:40] 获取到输出，行数: 1
[2025-07-16 21:37:40] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:40] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:40] 用户邮箱: 
[2025-07-16 21:37:40] 认证状态: 已认证
[2025-07-16 21:37:40] 是否已认证: True
[2025-07-16 21:37:40] === 更新窗体标题 ===
[2025-07-16 21:37:40] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:37:40] === 认证状态检查完成 ===
[2025-07-16 21:37:40] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:40] 认证状态：已认证
[2025-07-16 21:37:40] === 自动认证检查完成 ===
[2025-07-16 21:37:40] 认证状态：已认证
[2025-07-16 21:37:40] === 自动认证检查完成 ===
[2025-07-16 21:37:45] === 开始状态检查 ===
[2025-07-16 21:37:45] === 开始检查认证状态 ===
[2025-07-16 21:37:45] 设置 Process 参数
[2025-07-16 21:37:45] 设置 Process 选项
[2025-07-16 21:37:45] 开始执行认证状态检查
[2025-07-16 21:37:45] 
[2025-07-16 21:37:47] 认证状态检查完成，退出代码: 0
[2025-07-16 21:37:47] 获取到输出，行数: 1
[2025-07-16 21:37:47] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:47] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:47] 用户邮箱: 
[2025-07-16 21:37:47] 认证状态: 已认证
[2025-07-16 21:37:47] 是否已认证: True
[2025-07-16 21:37:47] === 更新窗体标题 ===
[2025-07-16 21:37:47] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:37:47] === 认证状态检查完成 ===
[2025-07-16 21:37:47] === 状态检查完成 ===
[2025-07-16 21:37:54] === 开始发送问题 ===
[2025-07-16 21:37:54] 用户问题: 你好
[2025-07-16 21:37:54] 禁用按钮和输入框
[2025-07-16 21:37:54] 聊天计数: 1
[2025-07-16 21:37:54] 开始调用 SendToAI
[2025-07-16 21:37:54] 进入 SendToAI 方法，问题: 你好
[2025-07-16 21:37:54] 显示发送状态
[2025-07-16 21:37:54] 创建 TProcess
[2025-07-16 21:37:54] 设置 Process 参数
[2025-07-16 21:37:54] 设置 Process 选项
[2025-07-16 21:37:54] 开始执行 Process (异步模式)
[2025-07-16 21:37:54] Process 启动成功，等待完成...
[2025-07-16 21:37:59] 等待进程完成... 5秒
[2025-07-16 21:38:05] 等待进程完成... 10秒
[2025-07-16 21:38:10] 等待进程完成... 15秒
[2025-07-16 21:38:16] 等待进程完成... 20秒
[2025-07-16 21:38:22] 等待进程完成... 25秒
[2025-07-16 21:38:27] 等待进程完成... 30秒
[2025-07-16 21:38:29] Process 执行完成
[2025-07-16 21:38:29] Process 退出代码: 0
[2025-07-16 21:38:34] 开始逐步测试 SessionUtils 函数
[2025-07-16 21:38:34] 会话目录: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:38:34] 步骤1：检查会话文件: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-16 21:38:34] ✅ 会话文件存在
[2025-07-16 21:38:34] 步骤2：测试 GetSessionInfo
[2025-07-16 21:38:34] ✅ GetSessionInfo 成功
[2025-07-16 21:38:34] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:38:34] 消息数量: 4
[2025-07-16 21:38:34] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-16 21:40:14] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-16 21:40:14] SendToAI 返回，回复长度: 0
[2025-07-16 21:45:06] === GUI 程序启动 ===
[2025-07-16 21:45:06] 开始创建窗体
[2025-07-16 21:45:06] 设置窗体属性
[2025-07-16 21:45:06] 创建 GUI 控件
[2025-07-16 21:45:06] 开始创建 GUI 控件
[2025-07-16 21:45:06] 创建聊天显示区
[2025-07-16 21:45:06] GUI 控件创建完成
[2025-07-16 21:45:06] 创建初始化定时器
[2025-07-16 21:45:06] 初始化变量
[2025-07-16 21:45:06] 开始初始化会话
[2025-07-16 21:45:06] 开始初始化会话
[2025-07-16 21:45:06] 设置配置文件路径
[2025-07-16 21:45:06] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:45:06] 检查 acli.exe
[2025-07-16 21:45:06] 会话初始化成功
[2025-07-16 21:45:06] 界面初始化完成
[2025-07-16 21:45:06] 启动自动认证检查定时器
[2025-07-16 21:45:06] === GUI 程序启动完成 ===
[2025-07-16 21:45:06] === 窗体显示事件触发 ===
[2025-07-16 21:45:06] === 开始自动认证检查 ===
[2025-07-16 21:45:06] 调用 CheckAuthStatus
[2025-07-16 21:45:06] === 开始检查认证状态 ===
[2025-07-16 21:45:06] 设置 Process 参数
[2025-07-16 21:45:06] 设置 Process 选项
[2025-07-16 21:45:06] 开始执行认证状态检查
[2025-07-16 21:45:06] 命令：
[2025-07-16 21:45:08] 认证状态检查完成，退出代码: 0
[2025-07-16 21:45:08] 获取到输出，行数: 1
[2025-07-16 21:45:08] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:08] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:08] 用户邮箱: 
[2025-07-16 21:45:08] 认证状态: 已认证
[2025-07-16 21:45:08] 是否已认证: True
[2025-07-16 21:45:08] === 更新窗体标题 ===
[2025-07-16 21:45:08] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:45:08] === 认证状态检查完成 ===
[2025-07-16 21:45:08] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:08] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:45:08] === 开始自动认证检查 ===
[2025-07-16 21:45:08] 调用 CheckAuthStatus
[2025-07-16 21:45:08] === 开始检查认证状态 ===
[2025-07-16 21:45:08] 设置 Process 参数
[2025-07-16 21:45:08] 设置 Process 选项
[2025-07-16 21:45:08] 开始执行认证状态检查
[2025-07-16 21:45:08] 命令：
[2025-07-16 21:45:10] 认证状态检查完成，退出代码: 0
[2025-07-16 21:45:10] 获取到输出，行数: 1
[2025-07-16 21:45:10] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:10] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:10] 用户邮箱: 
[2025-07-16 21:45:10] 认证状态: 已认证
[2025-07-16 21:45:10] 是否已认证: True
[2025-07-16 21:45:10] === 更新窗体标题 ===
[2025-07-16 21:45:10] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:45:10] === 认证状态检查完成 ===
[2025-07-16 21:45:10] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:10] 认证状态：已认证
[2025-07-16 21:45:10] === 自动认证检查完成 ===
[2025-07-16 21:45:10] 认证状态：已认证
[2025-07-16 21:45:10] === 自动认证检查完成 ===
[2025-07-16 21:45:23] === 开始发送问题 ===
[2025-07-16 21:45:23] 用户问题: 你好
[2025-07-16 21:45:23] 禁用按钮和输入框
[2025-07-16 21:45:23] 聊天计数: 1
[2025-07-16 21:45:23] 开始调用 SendToAI
[2025-07-16 21:45:23] 进入 SendToAI 方法，问题: 你好
[2025-07-16 21:45:23] 显示发送状态
[2025-07-16 21:45:23] 创建 TProcess
[2025-07-16 21:45:23] 设置 Process 参数
[2025-07-16 21:45:23] 设置 Process 选项
[2025-07-16 21:45:23] 开始执行 Process (异步模式)
[2025-07-16 21:45:23] 命令：
[2025-07-16 21:45:23] Process 启动成功，等待完成...
[2025-07-16 21:45:28] 等待进程完成... 5秒
[2025-07-16 21:45:34] 等待进程完成... 10秒
[2025-07-16 21:45:39] 等待进程完成... 15秒
[2025-07-16 21:45:45] 等待进程完成... 20秒
[2025-07-16 21:45:50] 等待进程完成... 25秒
[2025-07-16 21:45:56] 等待进程完成... 30秒
[2025-07-16 21:45:59] Process 执行完成
[2025-07-16 21:45:59] Process 退出代码: 0
[2025-07-16 21:46:04] 开始逐步测试 SessionUtils 函数
[2025-07-16 21:46:04] 会话目录: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:46:04] 步骤1：检查会话文件: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-16 21:46:04] ✅ 会话文件存在
[2025-07-16 21:46:04] 步骤2：测试 GetSessionInfo
[2025-07-16 21:46:04] ✅ GetSessionInfo 成功
[2025-07-16 21:46:04] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:46:04] 消息数量: 6
[2025-07-16 21:46:04] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-16 21:48:01] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-16 21:48:01] SendToAI 返回，回复长度: 0
[2025-07-17 06:01:18] === GUI 程序启动 ===
[2025-07-17 06:01:18] 开始创建窗体
[2025-07-17 06:01:18] 设置窗体属性
[2025-07-17 06:01:18] 创建 GUI 控件
[2025-07-17 06:01:18] 开始创建 GUI 控件
[2025-07-17 06:01:18] 创建聊天显示区
[2025-07-17 06:01:18] GUI 控件创建完成
[2025-07-17 06:01:18] 创建初始化定时器
[2025-07-17 06:01:18] 初始化变量
[2025-07-17 06:01:18] 开始初始化会话
[2025-07-17 06:01:18] 开始初始化会话
[2025-07-17 06:01:18] 设置配置文件路径
[2025-07-17 06:01:18] 检查配置文件: custom_persistence_config.yml
[2025-07-17 06:01:18] 检查 acli.exe
[2025-07-17 06:01:18] 会话初始化成功
[2025-07-17 06:01:18] 界面初始化完成
[2025-07-17 06:01:18] 启动自动认证检查定时器
[2025-07-17 06:01:18] === GUI 程序启动完成 ===
[2025-07-17 06:01:19] === 窗体显示事件触发 ===
[2025-07-17 06:01:19] === 开始自动认证检查 ===
[2025-07-17 06:01:19] 调用 CheckAuthStatus
[2025-07-17 06:01:19] === 开始检查认证状态 ===
[2025-07-17 06:01:19] 设置 Process 参数
[2025-07-17 06:01:19] 设置 Process 选项
[2025-07-17 06:01:19] 开始执行认证状态检查
[2025-07-17 06:01:19] 命令：
[2025-07-17 06:01:22] 认证状态检查完成，退出代码: 0
[2025-07-17 06:01:22] 获取到输出，行数: 1
[2025-07-17 06:01:22] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:22] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:22] 用户邮箱: 
[2025-07-17 06:01:22] 认证状态: 已认证
[2025-07-17 06:01:22] 是否已认证: True
[2025-07-17 06:01:22] === 更新窗体标题 ===
[2025-07-17 06:01:22] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:01:22] === 认证状态检查完成 ===
[2025-07-17 06:01:22] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:22] === 定时器触发，开始自动认证检查 ===
[2025-07-17 06:01:22] === 开始自动认证检查 ===
[2025-07-17 06:01:22] 调用 CheckAuthStatus
[2025-07-17 06:01:22] === 开始检查认证状态 ===
[2025-07-17 06:01:22] 设置 Process 参数
[2025-07-17 06:01:22] 设置 Process 选项
[2025-07-17 06:01:22] 开始执行认证状态检查
[2025-07-17 06:01:22] 命令：
[2025-07-17 06:01:25] 认证状态检查完成，退出代码: 0
[2025-07-17 06:01:25] 获取到输出，行数: 1
[2025-07-17 06:01:25] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:25] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:25] 用户邮箱: 
[2025-07-17 06:01:25] 认证状态: 已认证
[2025-07-17 06:01:25] 是否已认证: True
[2025-07-17 06:01:25] === 更新窗体标题 ===
[2025-07-17 06:01:25] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:01:25] === 认证状态检查完成 ===
[2025-07-17 06:01:25] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:25] 认证状态：已认证
[2025-07-17 06:01:25] === 自动认证检查完成 ===
[2025-07-17 06:01:25] 认证状态：已认证
[2025-07-17 06:01:25] === 自动认证检查完成 ===
[2025-07-17 06:01:33] === 开始发送问题 ===
[2025-07-17 06:01:33] 用户问题: 你好
[2025-07-17 06:01:33] 禁用按钮和输入框
[2025-07-17 06:01:33] 聊天计数: 1
[2025-07-17 06:01:33] 开始调用 SendToAI
[2025-07-17 06:01:33] 进入 SendToAI 方法，问题: 你好
[2025-07-17 06:01:33] 显示发送状态
[2025-07-17 06:01:33] 创建 TProcess
[2025-07-17 06:01:33] 设置 Process 参数
[2025-07-17 06:01:33] 设置 Process 选项
[2025-07-17 06:01:33] 开始执行 Process (异步模式)
[2025-07-17 06:01:33] 命令：
[2025-07-17 06:01:33] Process 启动成功，等待完成...
[2025-07-17 06:01:38] 等待进程完成... 5秒
[2025-07-17 06:01:44] 等待进程完成... 10秒
[2025-07-17 06:01:49] 等待进程完成... 15秒
[2025-07-17 06:01:55] 等待进程完成... 20秒
[2025-07-17 06:02:00] 等待进程完成... 25秒
[2025-07-17 06:02:06] 等待进程完成... 30秒
[2025-07-17 06:02:11] 等待进程完成... 35秒
[2025-07-17 06:02:17] 等待进程完成... 40秒
[2025-07-17 06:02:20] Process 执行完成
[2025-07-17 06:02:20] Process 退出代码: 0
[2025-07-17 06:02:25] 开始逐步测试 SessionUtils 函数
[2025-07-17 06:02:25] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 06:02:25] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 06:02:25] ✅ 会话文件存在
[2025-07-17 06:02:26] 步骤2：测试 GetSessionInfo
[2025-07-17 06:02:26] ✅ GetSessionInfo 成功
[2025-07-17 06:02:26] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 06:02:26] 消息数量: 8
[2025-07-17 06:02:26] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-17 06:02:26] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-17 06:02:26] SendToAI 返回，回复长度: 0
[2025-07-17 06:08:05] === GUI 程序启动 ===
[2025-07-17 06:08:05] 开始创建窗体
[2025-07-17 06:08:05] 设置窗体属性
[2025-07-17 06:08:05] 创建 GUI 控件
[2025-07-17 06:08:05] 开始创建 GUI 控件
[2025-07-17 06:08:05] 创建聊天显示区
[2025-07-17 06:08:05] GUI 控件创建完成
[2025-07-17 06:08:05] 创建初始化定时器
[2025-07-17 06:08:05] 初始化变量
[2025-07-17 06:08:05] 开始初始化会话
[2025-07-17 06:08:05] 开始初始化会话
[2025-07-17 06:08:05] 设置配置文件路径
[2025-07-17 06:08:05] 检查配置文件: custom_persistence_config.yml
[2025-07-17 06:08:05] 检查 acli.exe
[2025-07-17 06:08:05] 会话初始化成功
[2025-07-17 06:08:05] 界面初始化完成
[2025-07-17 06:08:05] 启动自动认证检查定时器
[2025-07-17 06:08:05] === GUI 程序启动完成 ===
[2025-07-17 06:08:05] === 窗体显示事件触发 ===
[2025-07-17 06:08:05] === 开始自动认证检查 ===
[2025-07-17 06:08:05] 调用 CheckAuthStatus
[2025-07-17 06:08:05] === 开始检查认证状态 ===
[2025-07-17 06:08:05] 设置 Process 参数
[2025-07-17 06:08:05] 设置 Process 选项
[2025-07-17 06:08:05] 开始执行认证状态检查
[2025-07-17 06:08:05] 命令：
[2025-07-17 06:08:07] 认证状态检查完成，退出代码: 0
[2025-07-17 06:08:07] 获取到输出，行数: 1
[2025-07-17 06:08:07] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:07] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:07] 用户邮箱: 
[2025-07-17 06:08:07] 认证状态: 已认证
[2025-07-17 06:08:07] 是否已认证: True
[2025-07-17 06:08:07] === 更新窗体标题 ===
[2025-07-17 06:08:07] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:08:07] === 认证状态检查完成 ===
[2025-07-17 06:08:07] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:07] === 定时器触发，开始自动认证检查 ===
[2025-07-17 06:08:07] === 开始自动认证检查 ===
[2025-07-17 06:08:07] 调用 CheckAuthStatus
[2025-07-17 06:08:07] === 开始检查认证状态 ===
[2025-07-17 06:08:07] 设置 Process 参数
[2025-07-17 06:08:07] 设置 Process 选项
[2025-07-17 06:08:07] 开始执行认证状态检查
[2025-07-17 06:08:07] 命令：
[2025-07-17 06:08:09] 认证状态检查完成，退出代码: 0
[2025-07-17 06:08:09] 获取到输出，行数: 1
[2025-07-17 06:08:09] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:09] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:09] 用户邮箱: 
[2025-07-17 06:08:09] 认证状态: 已认证
[2025-07-17 06:08:09] 是否已认证: True
[2025-07-17 06:08:09] === 更新窗体标题 ===
[2025-07-17 06:08:09] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:08:09] === 认证状态检查完成 ===
[2025-07-17 06:08:09] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:09] 认证状态：已认证
[2025-07-17 06:08:09] === 自动认证检查完成 ===
[2025-07-17 06:08:09] 认证状态：已认证
[2025-07-17 06:08:09] === 自动认证检查完成 ===
[2025-07-17 06:08:32] === 开始发送问题 ===
[2025-07-17 06:08:32] 用户问题: 测试
[2025-07-17 06:08:32] 禁用按钮和输入框
[2025-07-17 06:08:32] 聊天计数: 1
[2025-07-17 06:08:32] 开始调用 SendToAI
[2025-07-17 06:08:32] 进入 SendToAI 方法，问题: 测试
[2025-07-17 06:08:32] 显示发送状态
[2025-07-17 06:08:32] 创建 TProcess
[2025-07-17 06:08:32] 设置 Process 参数
[2025-07-17 06:08:32] 设置 Process 选项
[2025-07-17 06:08:32] 开始执行 Process (异步模式)
[2025-07-17 06:08:32] 命令：
[2025-07-17 06:08:32] Process 启动成功，等待完成...
[2025-07-17 06:08:38] 等待进程完成... 5秒
[2025-07-17 06:08:43] 等待进程完成... 10秒
[2025-07-17 06:08:49] 等待进程完成... 15秒
[2025-07-17 06:08:54] 等待进程完成... 20秒
[2025-07-17 06:09:00] 等待进程完成... 25秒
[2025-07-17 06:09:05] 等待进程完成... 30秒
[2025-07-17 06:09:11] 等待进程完成... 35秒
[2025-07-17 06:09:16] 等待进程完成... 40秒
[2025-07-17 06:09:22] 等待进程完成... 45秒
[2025-07-17 06:09:27] 等待进程完成... 50秒
[2025-07-17 06:09:32] 等待进程完成... 55秒
[2025-07-17 06:09:38] 等待进程完成... 60秒
[2025-07-17 06:09:38] 进程超时，强制终止
[2025-07-17 06:09:38] SendToAI 返回，回复长度: 0
[2025-07-17 06:17:59] === GUI 程序启动 ===
[2025-07-17 06:17:59] 开始创建窗体
[2025-07-17 06:17:59] 设置窗体属性
[2025-07-17 06:17:59] 创建 GUI 控件
[2025-07-17 06:17:59] 开始创建 GUI 控件
[2025-07-17 06:17:59] 创建聊天显示区
[2025-07-17 06:17:59] GUI 控件创建完成
[2025-07-17 06:17:59] 创建初始化定时器
[2025-07-17 06:17:59] 初始化变量
[2025-07-17 06:17:59] 开始初始化会话
[2025-07-17 06:17:59] 开始初始化会话
[2025-07-17 06:17:59] 设置配置文件路径
[2025-07-17 06:17:59] 检查配置文件: custom_persistence_config.yml
[2025-07-17 06:17:59] 检查 acli.exe
[2025-07-17 06:17:59] 会话初始化成功
[2025-07-17 06:17:59] 界面初始化完成
[2025-07-17 06:17:59] 启动自动认证检查定时器
[2025-07-17 06:17:59] === GUI 程序启动完成 ===
[2025-07-17 06:17:59] === 窗体显示事件触发 ===
[2025-07-17 06:17:59] === 开始自动认证检查 ===
[2025-07-17 06:17:59] 调用 CheckAuthStatus
[2025-07-17 06:17:59] === 开始检查认证状态 ===
[2025-07-17 06:17:59] 设置 Process 参数
[2025-07-17 06:17:59] 设置 Process 选项
[2025-07-17 06:17:59] 开始执行认证状态检查
[2025-07-17 06:17:59] 命令：
[2025-07-17 06:18:01] 认证状态检查完成，退出代码: 0
[2025-07-17 06:18:01] 获取到输出，行数: 1
[2025-07-17 06:18:01] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:18:01] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:18:01] 用户邮箱: 
[2025-07-17 06:18:01] 认证状态: 已认证
[2025-07-17 06:18:01] 是否已认证: True
[2025-07-17 06:18:01] === 更新窗体标题 ===
[2025-07-17 06:18:01] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:18:01] === 认证状态检查完成 ===
[2025-07-17 06:18:01] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:18:01] === 定时器触发，开始自动认证检查 ===
[2025-07-17 06:18:01] === 开始自动认证检查 ===
[2025-07-17 06:18:01] 调用 CheckAuthStatus
[2025-07-17 06:18:01] === 开始检查认证状态 ===
[2025-07-17 06:18:01] 设置 Process 参数
[2025-07-17 06:18:01] 设置 Process 选项
[2025-07-17 06:18:01] 开始执行认证状态检查
[2025-07-17 06:18:01] 命令：
[2025-07-17 06:18:04] 认证状态检查完成，退出代码: 0
[2025-07-17 06:18:04] 获取到输出，行数: 1
[2025-07-17 06:18:04] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:18:04] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:18:04] 用户邮箱: 
[2025-07-17 06:18:04] 认证状态: 已认证
[2025-07-17 06:18:04] 是否已认证: True
[2025-07-17 06:18:04] === 更新窗体标题 ===
[2025-07-17 06:18:04] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:18:04] === 认证状态检查完成 ===
[2025-07-17 06:18:04] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:18:04] 认证状态：已认证
[2025-07-17 06:18:04] === 自动认证检查完成 ===
[2025-07-17 06:18:04] 认证状态：已认证
[2025-07-17 06:18:04] === 自动认证检查完成 ===
[2025-07-17 04:24:12] === 开始发送问题 ===
[2025-07-17 04:24:12] 用户问题: 测试
[2025-07-17 04:24:12] 禁用按钮和输入框
[2025-07-17 04:24:12] 聊天计数: 1
[2025-07-17 04:24:12] 开始调用 SendToAI
[2025-07-17 04:24:12] 进入 SendToAI 方法，问题: 测试
[2025-07-17 04:24:12] 显示发送状态
[2025-07-17 04:24:12] 创建 TProcess
[2025-07-17 04:24:12] 设置 Process 参数
[2025-07-17 04:24:12] 设置 Process 选项
[2025-07-17 04:24:12] 开始执行 Process (异步模式)
[2025-07-17 04:24:12] 命令：
[2025-07-17 04:24:12] Process 启动成功，等待完成...
[2025-07-17 04:24:18] 等待进程完成... 5秒
[2025-07-17 04:24:23] 等待进程完成... 10秒
[2025-07-17 04:24:29] 等待进程完成... 15秒
[2025-07-17 04:24:34] 等待进程完成... 20秒
[2025-07-17 04:24:40] 等待进程完成... 25秒
[2025-07-17 04:24:45] 等待进程完成... 30秒
[2025-07-17 04:24:51] 等待进程完成... 35秒
[2025-07-17 04:24:56] 等待进程完成... 40秒
[2025-07-17 04:25:02] 等待进程完成... 45秒
[2025-07-17 04:25:07] 等待进程完成... 50秒
[2025-07-17 04:25:13] 等待进程完成... 55秒
[2025-07-17 04:25:19] 等待进程完成... 60秒
[2025-07-17 04:25:19] 进程超时，强制终止
[2025-07-17 04:25:19] SendToAI 返回，回复长度: 0
[2025-07-17 08:53:47] === GUI 程序启动 ===
[2025-07-17 08:53:47] 开始创建窗体
[2025-07-17 08:53:47] 设置窗体属性
[2025-07-17 08:53:47] 创建 GUI 控件
[2025-07-17 08:53:47] 开始创建 GUI 控件
[2025-07-17 08:53:47] 创建聊天显示区
[2025-07-17 08:53:47] GUI 控件创建完成
[2025-07-17 08:53:47] 创建初始化定时器
[2025-07-17 08:53:47] 初始化变量
[2025-07-17 08:53:47] 开始初始化会话
[2025-07-17 08:53:47] 开始初始化会话
[2025-07-17 08:53:47] 设置配置文件路径
[2025-07-17 08:53:47] 检查配置文件: custom_persistence_config.yml
[2025-07-17 08:53:47] 检查 acli.exe
[2025-07-17 08:53:47] 会话初始化成功
[2025-07-17 08:53:47] 界面初始化完成
[2025-07-17 08:53:47] 启动自动认证检查定时器
[2025-07-17 08:53:47] === GUI 程序启动完成 ===
[2025-07-17 08:53:47] === 窗体显示事件触发 ===
[2025-07-17 08:53:47] === 开始自动认证检查 ===
[2025-07-17 08:53:48] 调用 CheckAuthStatus
[2025-07-17 08:53:48] === 开始检查认证状态 ===
[2025-07-17 08:53:48] 设置 Process 参数
[2025-07-17 08:53:48] 设置 Process 选项
[2025-07-17 08:53:48] 开始执行认证状态检查
[2025-07-17 08:53:48] 命令：
[2025-07-17 08:53:50] 认证状态检查完成，退出代码: 0
[2025-07-17 08:53:50] 获取到输出，行数: 1
[2025-07-17 08:53:50] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 08:53:50] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 08:53:50] 用户邮箱: 
[2025-07-17 08:53:50] 认证状态: 已认证
[2025-07-17 08:53:50] 是否已认证: True
[2025-07-17 08:53:50] === 更新窗体标题 ===
[2025-07-17 08:53:50] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 08:53:50] === 认证状态检查完成 ===
[2025-07-17 08:53:50] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 08:53:50] === 定时器触发，开始自动认证检查 ===
[2025-07-17 08:53:50] === 开始自动认证检查 ===
[2025-07-17 08:53:50] 调用 CheckAuthStatus
[2025-07-17 08:53:50] === 开始检查认证状态 ===
[2025-07-17 08:53:50] 设置 Process 参数
[2025-07-17 08:53:50] 设置 Process 选项
[2025-07-17 08:53:50] 开始执行认证状态检查
[2025-07-17 08:53:50] 命令：
[2025-07-17 08:53:52] 认证状态检查完成，退出代码: 0
[2025-07-17 08:53:52] 获取到输出，行数: 1
[2025-07-17 08:53:52] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 08:53:52] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 08:53:52] 用户邮箱: 
[2025-07-17 08:53:52] 认证状态: 已认证
[2025-07-17 08:53:52] 是否已认证: True
[2025-07-17 08:53:52] === 更新窗体标题 ===
[2025-07-17 08:53:52] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 08:53:52] === 认证状态检查完成 ===
[2025-07-17 08:53:52] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 08:53:52] 认证状态：已认证
[2025-07-17 08:53:52] === 自动认证检查完成 ===
[2025-07-17 08:53:52] 认证状态：已认证
[2025-07-17 08:53:52] === 自动认证检查完成 ===
[2025-07-17 10:11:23] === GUI 程序启动 ===
[2025-07-17 10:11:23] 开始创建窗体
[2025-07-17 10:11:23] 设置窗体属性
[2025-07-17 10:11:23] 创建 GUI 控件
[2025-07-17 10:11:23] 开始创建 GUI 控件
[2025-07-17 10:11:23] 创建聊天显示区
[2025-07-17 10:11:23] GUI 控件创建完成
[2025-07-17 10:11:23] 创建初始化定时器
[2025-07-17 10:11:23] 初始化变量
[2025-07-17 10:11:23] 开始初始化会话
[2025-07-17 10:11:23] 开始初始化会话
[2025-07-17 10:11:23] 设置配置文件路径
[2025-07-17 10:11:23] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:11:23] 检查 acli.exe
[2025-07-17 10:11:23] 会话初始化成功
[2025-07-17 10:11:23] 界面初始化完成
[2025-07-17 10:11:23] 启动自动认证检查定时器
[2025-07-17 10:11:23] === GUI 程序启动完成 ===
[2025-07-17 10:11:23] === 窗体显示事件触发 ===
[2025-07-17 10:11:23] === 开始自动认证检查 ===
[2025-07-17 10:11:23] 调用 CheckAuthStatus
[2025-07-17 10:11:23] === 开始检查认证状态 ===
[2025-07-17 10:11:23] 设置 Process 参数
[2025-07-17 10:11:23] 设置 Process 选项
[2025-07-17 10:11:23] 开始执行认证状态检查
[2025-07-17 10:11:23] 命令：
[2025-07-17 10:11:25] 认证状态检查完成，退出代码: 0
[2025-07-17 10:11:25] 获取到输出，行数: 1
[2025-07-17 10:11:25] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:11:25] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:11:25] 用户邮箱: 
[2025-07-17 10:11:25] 认证状态: 已认证
[2025-07-17 10:11:25] 是否已认证: True
[2025-07-17 10:11:25] === 更新窗体标题 ===
[2025-07-17 10:11:25] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:11:25] === 认证状态检查完成 ===
[2025-07-17 10:11:25] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:11:25] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:11:25] === 开始自动认证检查 ===
[2025-07-17 10:11:25] 调用 CheckAuthStatus
[2025-07-17 10:11:25] === 开始检查认证状态 ===
[2025-07-17 10:11:25] 设置 Process 参数
[2025-07-17 10:11:25] 设置 Process 选项
[2025-07-17 10:11:25] 开始执行认证状态检查
[2025-07-17 10:11:25] 命令：
[2025-07-17 10:11:28] 认证状态检查完成，退出代码: 0
[2025-07-17 10:11:28] 获取到输出，行数: 1
[2025-07-17 10:11:28] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:11:28] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:11:28] 用户邮箱: 
[2025-07-17 10:11:28] 认证状态: 已认证
[2025-07-17 10:11:28] 是否已认证: True
[2025-07-17 10:11:28] === 更新窗体标题 ===
[2025-07-17 10:11:28] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:11:28] === 认证状态检查完成 ===
[2025-07-17 10:11:28] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:11:28] 认证状态：已认证
[2025-07-17 10:11:28] === 自动认证检查完成 ===
[2025-07-17 10:11:28] 认证状态：已认证
[2025-07-17 10:11:28] === 自动认证检查完成 ===
[2025-07-17 10:11:42] === 开始发送问题 ===
[2025-07-17 10:11:42] 用户问题: 你好
[2025-07-17 10:11:42] 禁用按钮和输入框
[2025-07-17 10:11:42] 聊天计数: 1
[2025-07-17 10:11:42] 开始调用 SendToAI
[2025-07-17 10:11:42] 进入 SendToAI 方法，问题: 你好
[2025-07-17 10:11:42] 显示发送状态
[2025-07-17 10:11:42] 创建 TProcess
[2025-07-17 10:11:42] 设置 Process 参数
[2025-07-17 10:11:42] 设置 Process 选项
[2025-07-17 10:11:42] 开始执行 Process (异步模式)
[2025-07-17 10:11:42] 命令：
[2025-07-17 10:11:42] Process 启动成功，等待完成...
[2025-07-17 10:11:47] 等待进程完成... 5秒
[2025-07-17 10:11:53] 等待进程完成... 10秒
[2025-07-17 10:11:58] 等待进程完成... 15秒
[2025-07-17 10:12:04] 等待进程完成... 20秒
[2025-07-17 10:12:09] 等待进程完成... 25秒
[2025-07-17 10:12:15] 等待进程完成... 30秒
[2025-07-17 10:12:20] 等待进程完成... 35秒
[2025-07-17 10:12:24] Process 执行完成
[2025-07-17 10:12:24] Process 退出代码: 0
[2025-07-17 10:12:29] 开始逐步测试 SessionUtils 函数
[2025-07-17 10:12:29] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2_response_cache.txt
[2025-07-17 10:12:29] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2_response_cache.txt\session_context.json
[2025-07-17 10:12:29] ❌ 会话文件不存在
[2025-07-17 10:12:29] SendToAI 返回，回复长度: 0
[2025-07-17 10:15:28] === GUI 程序启动 ===
[2025-07-17 10:15:28] 开始创建窗体
[2025-07-17 10:15:28] 设置窗体属性
[2025-07-17 10:15:28] 创建 GUI 控件
[2025-07-17 10:15:28] 开始创建 GUI 控件
[2025-07-17 10:15:28] 创建聊天显示区
[2025-07-17 10:15:28] GUI 控件创建完成
[2025-07-17 10:15:28] 创建初始化定时器
[2025-07-17 10:15:28] 初始化变量
[2025-07-17 10:15:28] 开始初始化会话
[2025-07-17 10:15:28] 开始初始化会话
[2025-07-17 10:15:28] 设置配置文件路径
[2025-07-17 10:15:28] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:15:28] 检查 acli.exe
[2025-07-17 10:15:28] 会话初始化成功
[2025-07-17 10:15:28] 界面初始化完成
[2025-07-17 10:15:28] 启动自动认证检查定时器
[2025-07-17 10:15:28] === GUI 程序启动完成 ===
[2025-07-17 10:15:28] === 窗体显示事件触发 ===
[2025-07-17 10:15:28] === 开始自动认证检查 ===
[2025-07-17 10:15:28] 调用 CheckAuthStatus
[2025-07-17 10:15:28] === 开始检查认证状态 ===
[2025-07-17 10:15:28] 设置 Process 参数
[2025-07-17 10:15:28] 设置 Process 选项
[2025-07-17 10:15:28] 开始执行认证状态检查
[2025-07-17 10:15:28] 命令：
[2025-07-17 10:15:30] 认证状态检查完成，退出代码: 0
[2025-07-17 10:15:30] 获取到输出，行数: 1
[2025-07-17 10:15:30] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:15:30] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:15:30] 用户邮箱: 
[2025-07-17 10:15:30] 认证状态: 已认证
[2025-07-17 10:15:30] 是否已认证: True
[2025-07-17 10:15:30] === 更新窗体标题 ===
[2025-07-17 10:15:30] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:15:30] === 认证状态检查完成 ===
[2025-07-17 10:15:30] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:15:30] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:15:30] === 开始自动认证检查 ===
[2025-07-17 10:15:30] 调用 CheckAuthStatus
[2025-07-17 10:15:30] === 开始检查认证状态 ===
[2025-07-17 10:15:30] 设置 Process 参数
[2025-07-17 10:15:30] 设置 Process 选项
[2025-07-17 10:15:30] 开始执行认证状态检查
[2025-07-17 10:15:30] 命令：
[2025-07-17 10:15:37] 认证状态检查完成，退出代码: 0
[2025-07-17 10:15:37] 获取到输出，行数: 1
[2025-07-17 10:15:37] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:15:37] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:15:37] 用户邮箱: 
[2025-07-17 10:15:37] 认证状态: 已认证
[2025-07-17 10:15:37] 是否已认证: True
[2025-07-17 10:15:37] === 更新窗体标题 ===
[2025-07-17 10:15:37] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:15:37] === 认证状态检查完成 ===
[2025-07-17 10:15:37] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:15:37] 认证状态：已认证
[2025-07-17 10:15:37] === 自动认证检查完成 ===
[2025-07-17 10:15:37] 认证状态：已认证
[2025-07-17 10:15:37] === 自动认证检查完成 ===
[2025-07-17 10:16:33] === 开始发送问题 ===
[2025-07-17 10:16:33] 用户问题: 你好
[2025-07-17 10:16:33] 禁用按钮和输入框
[2025-07-17 10:16:33] 聊天计数: 1
[2025-07-17 10:16:33] 开始调用 SendToAI
[2025-07-17 10:16:33] 进入 SendToAI 方法，问题: 你好
[2025-07-17 10:16:33] 显示发送状态
[2025-07-17 10:16:33] 创建 TProcess
[2025-07-17 10:16:33] 设置 Process 参数
[2025-07-17 10:16:33] 设置 Process 选项
[2025-07-17 10:16:33] 开始执行 Process (异步模式)
[2025-07-17 10:16:33] 命令：
[2025-07-17 10:16:34] Process 启动成功，等待完成...
[2025-07-17 10:16:39] 等待进程完成... 5秒
[2025-07-17 10:16:45] 等待进程完成... 10秒
[2025-07-17 10:16:50] 等待进程完成... 15秒
[2025-07-17 10:16:56] 等待进程完成... 20秒
[2025-07-17 10:17:01] 等待进程完成... 25秒
[2025-07-17 10:17:07] 等待进程完成... 30秒
[2025-07-17 10:17:12] 等待进程完成... 35秒
[2025-07-17 10:17:13] Process 执行完成
[2025-07-17 10:17:13] Process 退出代码: 0
[2025-07-17 10:17:18] 开始逐步测试 SessionUtils 函数
[2025-07-17 10:17:18] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:17:18] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:17:18] ✅ 会话文件存在
[2025-07-17 10:17:18] 步骤2：测试 GetSessionInfo
[2025-07-17 10:17:18] ✅ GetSessionInfo 成功
[2025-07-17 10:17:18] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:17:18] 消息数量: 60
[2025-07-17 10:17:18] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-17 10:17:18] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-17 10:17:18] SendToAI 返回，回复长度: 0
[2025-07-17 10:18:58] === GUI 程序启动 ===
[2025-07-17 10:18:58] 开始创建窗体
[2025-07-17 10:18:58] 设置窗体属性
[2025-07-17 10:18:58] 创建 GUI 控件
[2025-07-17 10:18:58] 开始创建 GUI 控件
[2025-07-17 10:18:58] 创建聊天显示区
[2025-07-17 10:18:58] GUI 控件创建完成
[2025-07-17 10:18:58] 创建初始化定时器
[2025-07-17 10:18:58] 初始化变量
[2025-07-17 10:18:58] 开始初始化会话
[2025-07-17 10:18:58] 开始初始化会话
[2025-07-17 10:18:58] 设置配置文件路径
[2025-07-17 10:18:58] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:18:58] 检查 acli.exe
[2025-07-17 10:18:58] 会话初始化成功
[2025-07-17 10:18:58] 界面初始化完成
[2025-07-17 10:18:58] 启动自动认证检查定时器
[2025-07-17 10:18:58] === GUI 程序启动完成 ===
[2025-07-17 10:18:58] === 窗体显示事件触发 ===
[2025-07-17 10:18:58] === 开始自动认证检查 ===
[2025-07-17 10:18:58] 调用 CheckAuthStatus
[2025-07-17 10:18:58] === 开始检查认证状态 ===
[2025-07-17 10:18:58] 设置 Process 参数
[2025-07-17 10:18:58] 设置 Process 选项
[2025-07-17 10:18:58] 开始执行认证状态检查
[2025-07-17 10:18:58] 命令：
[2025-07-17 10:19:01] 认证状态检查完成，退出代码: 0
[2025-07-17 10:19:01] 获取到输出，行数: 1
[2025-07-17 10:19:01] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:19:01] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:19:01] 用户邮箱: 
[2025-07-17 10:19:01] 认证状态: 已认证
[2025-07-17 10:19:01] 是否已认证: True
[2025-07-17 10:19:01] === 更新窗体标题 ===
[2025-07-17 10:19:01] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:19:01] === 认证状态检查完成 ===
[2025-07-17 10:19:01] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:19:01] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:19:01] === 开始自动认证检查 ===
[2025-07-17 10:19:01] 调用 CheckAuthStatus
[2025-07-17 10:19:01] === 开始检查认证状态 ===
[2025-07-17 10:19:01] 设置 Process 参数
[2025-07-17 10:19:01] 设置 Process 选项
[2025-07-17 10:19:01] 开始执行认证状态检查
[2025-07-17 10:19:01] 命令：
[2025-07-17 10:19:03] 认证状态检查完成，退出代码: 0
[2025-07-17 10:19:03] 获取到输出，行数: 1
[2025-07-17 10:19:03] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:19:03] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:19:03] 用户邮箱: 
[2025-07-17 10:19:03] 认证状态: 已认证
[2025-07-17 10:19:03] 是否已认证: True
[2025-07-17 10:19:03] === 更新窗体标题 ===
[2025-07-17 10:19:03] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:19:03] === 认证状态检查完成 ===
[2025-07-17 10:19:03] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:19:03] 认证状态：已认证
[2025-07-17 10:19:03] === 自动认证检查完成 ===
[2025-07-17 10:19:03] 认证状态：已认证
[2025-07-17 10:19:03] === 自动认证检查完成 ===
[2025-07-17 10:19:49] === 开始发送问题 ===
[2025-07-17 10:19:49] 用户问题: 你好
[2025-07-17 10:19:49] 禁用按钮和输入框
[2025-07-17 10:19:49] 聊天计数: 1
[2025-07-17 10:19:49] 开始调用 SendToAI
[2025-07-17 10:19:49] 进入 SendToAI 方法，问题: 你好
[2025-07-17 10:19:49] 显示发送状态
[2025-07-17 10:19:49] 创建 TProcess
[2025-07-17 10:19:49] 设置 Process 参数
[2025-07-17 10:19:49] 设置 Process 选项
[2025-07-17 10:19:49] 开始执行 Process (异步模式)
[2025-07-17 10:19:49] 命令：
[2025-07-17 10:19:49] Process 启动成功，等待完成...
[2025-07-17 10:19:54] 等待进程完成... 5秒
[2025-07-17 10:20:00] 等待进程完成... 10秒
[2025-07-17 10:20:05] 等待进程完成... 15秒
[2025-07-17 10:20:11] 等待进程完成... 20秒
[2025-07-17 10:20:16] 等待进程完成... 25秒
[2025-07-17 10:20:21] 等待进程完成... 30秒
[2025-07-17 10:20:27] 等待进程完成... 35秒
[2025-07-17 10:20:31] Process 执行完成
[2025-07-17 10:20:31] Process 退出代码: 0
[2025-07-17 10:20:37] 开始逐步测试 SessionUtils 函数
[2025-07-17 10:20:37] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:20:37] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:20:37] ✅ 会话文件存在
[2025-07-17 10:20:37] 步骤2：测试 GetSessionInfo
[2025-07-17 10:20:37] ✅ GetSessionInfo 成功
[2025-07-17 10:20:37] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:20:37] 消息数量: 62
[2025-07-17 10:20:37] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-17 10:20:37] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-17 10:20:37] SendToAI 返回，回复长度: 0
[2025-07-17 10:22:34] === GUI 程序启动 ===
[2025-07-17 10:22:34] 开始创建窗体
[2025-07-17 10:22:34] 设置窗体属性
[2025-07-17 10:22:34] 创建 GUI 控件
[2025-07-17 10:22:34] 开始创建 GUI 控件
[2025-07-17 10:22:34] 创建聊天显示区
[2025-07-17 10:22:34] GUI 控件创建完成
[2025-07-17 10:22:34] 创建初始化定时器
[2025-07-17 10:22:34] 初始化变量
[2025-07-17 10:22:34] 开始初始化会话
[2025-07-17 10:22:34] 开始初始化会话
[2025-07-17 10:22:34] 设置配置文件路径
[2025-07-17 10:22:34] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:22:34] 检查 acli.exe
[2025-07-17 10:22:34] 会话初始化成功
[2025-07-17 10:22:34] 界面初始化完成
[2025-07-17 10:22:34] 启动自动认证检查定时器
[2025-07-17 10:22:34] === GUI 程序启动完成 ===
[2025-07-17 10:22:34] === 窗体显示事件触发 ===
[2025-07-17 10:22:34] === 开始自动认证检查 ===
[2025-07-17 10:22:34] 调用 CheckAuthStatus
[2025-07-17 10:22:34] === 开始检查认证状态 ===
[2025-07-17 10:22:34] 设置 Process 参数
[2025-07-17 10:22:34] 设置 Process 选项
[2025-07-17 10:22:34] 开始执行认证状态检查
[2025-07-17 10:22:34] 命令：
[2025-07-17 10:22:36] 认证状态检查完成，退出代码: 0
[2025-07-17 10:22:36] 获取到输出，行数: 1
[2025-07-17 10:22:36] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:22:36] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:22:36] 用户邮箱: 
[2025-07-17 10:22:36] 认证状态: 已认证
[2025-07-17 10:22:36] 是否已认证: True
[2025-07-17 10:22:36] === 更新窗体标题 ===
[2025-07-17 10:22:36] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:22:36] === 认证状态检查完成 ===
[2025-07-17 10:22:36] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:22:36] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:22:36] === 开始自动认证检查 ===
[2025-07-17 10:22:36] 调用 CheckAuthStatus
[2025-07-17 10:22:36] === 开始检查认证状态 ===
[2025-07-17 10:22:36] 设置 Process 参数
[2025-07-17 10:22:36] 设置 Process 选项
[2025-07-17 10:22:36] 开始执行认证状态检查
[2025-07-17 10:22:36] 命令：
[2025-07-17 10:22:39] 认证状态检查完成，退出代码: 0
[2025-07-17 10:22:39] 获取到输出，行数: 1
[2025-07-17 10:22:39] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:22:39] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:22:39] 用户邮箱: 
[2025-07-17 10:22:39] 认证状态: 已认证
[2025-07-17 10:22:39] 是否已认证: True
[2025-07-17 10:22:39] === 更新窗体标题 ===
[2025-07-17 10:22:39] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:22:39] === 认证状态检查完成 ===
[2025-07-17 10:22:39] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:22:39] 认证状态：已认证
[2025-07-17 10:22:39] === 自动认证检查完成 ===
[2025-07-17 10:22:39] 认证状态：已认证
[2025-07-17 10:22:39] === 自动认证检查完成 ===
[2025-07-17 10:25:09] === GUI 程序启动 ===
[2025-07-17 10:25:09] 开始创建窗体
[2025-07-17 10:25:09] 设置窗体属性
[2025-07-17 10:25:09] 创建 GUI 控件
[2025-07-17 10:25:09] 开始创建 GUI 控件
[2025-07-17 10:25:09] 创建聊天显示区
[2025-07-17 10:25:09] GUI 控件创建完成
[2025-07-17 10:25:09] 创建初始化定时器
[2025-07-17 10:25:09] 初始化变量
[2025-07-17 10:25:09] 开始初始化会话
[2025-07-17 10:25:09] 开始初始化会话
[2025-07-17 10:25:09] 设置配置文件路径
[2025-07-17 10:25:09] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:25:09] 检查 acli.exe
[2025-07-17 10:25:09] 会话初始化成功
[2025-07-17 10:25:09] 界面初始化完成
[2025-07-17 10:25:09] 启动自动认证检查定时器
[2025-07-17 10:25:09] === GUI 程序启动完成 ===
[2025-07-17 10:25:09] === 窗体显示事件触发 ===
[2025-07-17 10:25:09] === 开始自动认证检查 ===
[2025-07-17 10:25:09] 调用 CheckAuthStatus
[2025-07-17 10:25:09] === 开始检查认证状态 ===
[2025-07-17 10:25:09] 设置 Process 参数
[2025-07-17 10:25:09] 设置 Process 选项
[2025-07-17 10:25:09] 开始执行认证状态检查
[2025-07-17 10:25:09] 命令：
[2025-07-17 10:25:14] 认证状态检查完成，退出代码: 0
[2025-07-17 10:25:14] 获取到输出，行数: 1
[2025-07-17 10:25:14] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:25:14] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:25:14] 用户邮箱: 
[2025-07-17 10:25:14] 认证状态: 已认证
[2025-07-17 10:25:14] 是否已认证: True
[2025-07-17 10:25:14] === 更新窗体标题 ===
[2025-07-17 10:25:14] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:25:14] === 认证状态检查完成 ===
[2025-07-17 10:25:14] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:25:14] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:25:14] === 开始自动认证检查 ===
[2025-07-17 10:25:14] 调用 CheckAuthStatus
[2025-07-17 10:25:14] === 开始检查认证状态 ===
[2025-07-17 10:25:14] 设置 Process 参数
[2025-07-17 10:25:14] 设置 Process 选项
[2025-07-17 10:25:14] 开始执行认证状态检查
[2025-07-17 10:25:14] 命令：
[2025-07-17 10:25:16] 认证状态检查完成，退出代码: 0
[2025-07-17 10:25:16] 获取到输出，行数: 1
[2025-07-17 10:25:16] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:25:16] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:25:16] 用户邮箱: 
[2025-07-17 10:25:16] 认证状态: 已认证
[2025-07-17 10:25:16] 是否已认证: True
[2025-07-17 10:25:16] === 更新窗体标题 ===
[2025-07-17 10:25:16] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:25:16] === 认证状态检查完成 ===
[2025-07-17 10:25:16] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:25:16] 认证状态：已认证
[2025-07-17 10:25:16] === 自动认证检查完成 ===
[2025-07-17 10:25:16] 认证状态：已认证
[2025-07-17 10:25:16] === 自动认证检查完成 ===
[2025-07-17 10:48:18] === 开始发送问题 ===
[2025-07-17 10:48:18] 用户问题: 你好
[2025-07-17 10:48:18] 禁用按钮和输入框
[2025-07-17 10:48:18] 聊天计数: 1
[2025-07-17 10:48:18] 开始调用 SendToAI
[2025-07-17 10:48:18] 进入 SendToAI 方法，问题: 你好
[2025-07-17 10:48:18] 显示发送状态
[2025-07-17 10:48:18] 创建 TProcess
[2025-07-17 10:48:18] 设置 Process 参数
[2025-07-17 10:48:18] 设置 Process 选项
[2025-07-17 10:48:18] 开始执行 Process (异步模式)
[2025-07-17 10:48:18] 命令：
[2025-07-17 10:48:18] Process 启动成功，等待完成...
[2025-07-17 10:48:23] 等待进程完成... 5秒
[2025-07-17 10:48:29] 等待进程完成... 10秒
[2025-07-17 10:48:34] 等待进程完成... 15秒
[2025-07-17 10:48:40] 等待进程完成... 20秒
[2025-07-17 10:48:45] 等待进程完成... 25秒
[2025-07-17 10:48:51] 等待进程完成... 30秒
[2025-07-17 10:48:56] 等待进程完成... 35秒
[2025-07-17 10:49:02] 等待进程完成... 40秒
[2025-07-17 10:49:08] 等待进程完成... 45秒
[2025-07-17 10:49:08] Process 执行完成
[2025-07-17 10:49:08] Process 退出代码: 0
[2025-07-17 10:49:13] 开始逐步测试 SessionUtils 函数
[2025-07-17 10:49:13] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:49:13] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:49:13] ✅ 会话文件存在
[2025-07-17 10:49:14] 步骤2：测试 GetSessionInfo
[2025-07-17 10:49:14] ✅ GetSessionInfo 成功
[2025-07-17 10:49:14] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:49:14] 消息数量: 64
[2025-07-17 10:49:14] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-17 10:49:14] 传递给函数的文件路径: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:49:14] 文件是否存在: True
[2025-07-17 10:49:14] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-17 10:49:14] SendToAI 返回，回复长度: 0
[2025-07-17 10:54:23] === GUI 程序启动 ===
[2025-07-17 10:54:23] 开始创建窗体
[2025-07-17 10:54:23] 设置窗体属性
[2025-07-17 10:54:23] 创建 GUI 控件
[2025-07-17 10:54:23] 开始创建 GUI 控件
[2025-07-17 10:54:23] 创建聊天显示区
[2025-07-17 10:54:23] GUI 控件创建完成
[2025-07-17 10:54:23] 创建初始化定时器
[2025-07-17 10:54:23] 初始化变量
[2025-07-17 10:54:23] 开始初始化会话
[2025-07-17 10:54:23] 开始初始化会话
[2025-07-17 10:54:23] 设置配置文件路径
[2025-07-17 10:54:23] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:54:23] 检查 acli.exe
[2025-07-17 10:54:23] 会话初始化成功
[2025-07-17 10:54:23] 界面初始化完成
[2025-07-17 10:54:23] 启动自动认证检查定时器
[2025-07-17 10:54:23] === GUI 程序启动完成 ===
[2025-07-17 10:54:23] === 窗体显示事件触发 ===
[2025-07-17 10:54:23] === 开始自动认证检查 ===
[2025-07-17 10:54:23] 调用 CheckAuthStatus
[2025-07-17 10:54:23] === 开始检查认证状态 ===
[2025-07-17 10:54:23] 设置 Process 参数
[2025-07-17 10:54:23] 设置 Process 选项
[2025-07-17 10:54:23] 开始执行认证状态检查
[2025-07-17 10:54:23] 命令：
[2025-07-17 10:54:25] 认证状态检查完成，退出代码: 0
[2025-07-17 10:54:25] 获取到输出，行数: 1
[2025-07-17 10:54:25] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:54:25] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:54:25] 用户邮箱: 
[2025-07-17 10:54:25] 认证状态: 已认证
[2025-07-17 10:54:25] 是否已认证: True
[2025-07-17 10:54:25] === 更新窗体标题 ===
[2025-07-17 10:54:25] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:54:25] === 认证状态检查完成 ===
[2025-07-17 10:54:25] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:54:25] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:54:25] === 开始自动认证检查 ===
[2025-07-17 10:54:25] 调用 CheckAuthStatus
[2025-07-17 10:54:25] === 开始检查认证状态 ===
[2025-07-17 10:54:25] 设置 Process 参数
[2025-07-17 10:54:25] 设置 Process 选项
[2025-07-17 10:54:25] 开始执行认证状态检查
[2025-07-17 10:54:25] 命令：
[2025-07-17 10:54:27] 认证状态检查完成，退出代码: 0
[2025-07-17 10:54:27] 获取到输出，行数: 1
[2025-07-17 10:54:27] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:54:27] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:54:27] 用户邮箱: 
[2025-07-17 10:54:27] 认证状态: 已认证
[2025-07-17 10:54:27] 是否已认证: True
[2025-07-17 10:54:27] === 更新窗体标题 ===
[2025-07-17 10:54:27] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:54:27] === 认证状态检查完成 ===
[2025-07-17 10:54:27] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:54:27] 认证状态：已认证
[2025-07-17 10:54:27] === 自动认证检查完成 ===
[2025-07-17 10:54:27] 认证状态：已认证
[2025-07-17 10:54:27] === 自动认证检查完成 ===
[2025-07-17 10:56:34] === GUI 程序启动 ===
[2025-07-17 10:56:34] 开始创建窗体
[2025-07-17 10:56:34] 设置窗体属性
[2025-07-17 10:56:34] 创建 GUI 控件
[2025-07-17 10:56:34] 开始创建 GUI 控件
[2025-07-17 10:56:34] 创建聊天显示区
[2025-07-17 10:56:34] GUI 控件创建完成
[2025-07-17 10:56:34] 创建初始化定时器
[2025-07-17 10:56:34] 初始化变量
[2025-07-17 10:56:34] 开始初始化会话
[2025-07-17 10:56:34] 开始初始化会话
[2025-07-17 10:56:34] 设置配置文件路径
[2025-07-17 10:56:34] 检查配置文件: custom_persistence_config.yml
[2025-07-17 10:56:34] 检查 acli.exe
[2025-07-17 10:56:34] 会话初始化成功
[2025-07-17 10:56:34] 界面初始化完成
[2025-07-17 10:56:34] 启动自动认证检查定时器
[2025-07-17 10:56:34] === GUI 程序启动完成 ===
[2025-07-17 10:56:34] === 窗体显示事件触发 ===
[2025-07-17 10:56:34] === 开始自动认证检查 ===
[2025-07-17 10:56:34] 调用 CheckAuthStatus
[2025-07-17 10:56:34] === 开始检查认证状态 ===
[2025-07-17 10:56:34] 设置 Process 参数
[2025-07-17 10:56:34] 设置 Process 选项
[2025-07-17 10:56:34] 开始执行认证状态检查
[2025-07-17 10:56:34] 命令：
[2025-07-17 10:56:36] 认证状态检查完成，退出代码: 0
[2025-07-17 10:56:36] 获取到输出，行数: 1
[2025-07-17 10:56:36] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:56:36] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:56:36] 用户邮箱: 
[2025-07-17 10:56:36] 认证状态: 已认证
[2025-07-17 10:56:36] 是否已认证: True
[2025-07-17 10:56:36] === 更新窗体标题 ===
[2025-07-17 10:56:36] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:56:36] === 认证状态检查完成 ===
[2025-07-17 10:56:36] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:56:36] === 定时器触发，开始自动认证检查 ===
[2025-07-17 10:56:36] === 开始自动认证检查 ===
[2025-07-17 10:56:36] 调用 CheckAuthStatus
[2025-07-17 10:56:36] === 开始检查认证状态 ===
[2025-07-17 10:56:36] 设置 Process 参数
[2025-07-17 10:56:36] 设置 Process 选项
[2025-07-17 10:56:36] 开始执行认证状态检查
[2025-07-17 10:56:36] 命令：
[2025-07-17 10:56:40] 认证状态检查完成，退出代码: 0
[2025-07-17 10:56:40] 获取到输出，行数: 1
[2025-07-17 10:56:40] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:56:40] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:56:40] 用户邮箱: 
[2025-07-17 10:56:40] 认证状态: 已认证
[2025-07-17 10:56:40] 是否已认证: True
[2025-07-17 10:56:40] === 更新窗体标题 ===
[2025-07-17 10:56:40] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 10:56:40] === 认证状态检查完成 ===
[2025-07-17 10:56:40] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 10:56:40] 认证状态：已认证
[2025-07-17 10:56:40] === 自动认证检查完成 ===
[2025-07-17 10:56:40] 认证状态：已认证
[2025-07-17 10:56:40] === 自动认证检查完成 ===
[2025-07-17 10:56:44] === 开始发送问题 ===
[2025-07-17 10:56:44] 用户问题: 你好
[2025-07-17 10:56:44] 禁用按钮和输入框
[2025-07-17 10:56:44] 聊天计数: 1
[2025-07-17 10:56:44] 开始调用 SendToAI
[2025-07-17 10:56:44] 进入 SendToAI 方法，问题: 你好
[2025-07-17 10:56:44] 显示发送状态
[2025-07-17 10:56:44] 创建 TProcess
[2025-07-17 10:56:44] 设置 Process 参数
[2025-07-17 10:56:44] 设置 Process 选项
[2025-07-17 10:56:44] 开始执行 Process (异步模式)
[2025-07-17 10:56:44] 命令：
[2025-07-17 10:56:44] Process 启动成功，等待完成...
[2025-07-17 10:56:50] 等待进程完成... 5秒
[2025-07-17 10:56:55] 等待进程完成... 10秒
[2025-07-17 10:57:00] 等待进程完成... 15秒
[2025-07-17 10:57:06] 等待进程完成... 20秒
[2025-07-17 10:57:11] 等待进程完成... 25秒
[2025-07-17 10:57:17] 等待进程完成... 30秒
[2025-07-17 10:57:22] 等待进程完成... 35秒
[2025-07-17 10:57:28] 等待进程完成... 40秒
[2025-07-17 10:57:33] 等待进程完成... 45秒
[2025-07-17 10:57:38] 等待进程完成... 50秒
[2025-07-17 10:57:39] Process 执行完成
[2025-07-17 10:57:39] Process 退出代码: 0
[2025-07-17 10:57:44] 开始逐步测试 SessionUtils 函数
[2025-07-17 10:57:44] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:57:44] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:57:44] ✅ 会话文件存在
[2025-07-17 10:57:44] 步骤2：测试 GetSessionInfo
[2025-07-17 10:57:44] ✅ GetSessionInfo 成功
[2025-07-17 10:57:44] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 10:57:44] 消息数量: 66
[2025-07-17 10:57:44] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-17 10:57:44] 传递给函数的文件路径: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:57:44] 文件是否存在: True
[2025-07-17 10:57:44] [SessionUtils] 🔧 ExtractLatestResponseFromJSON 开始，文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:57:44] [SessionUtils] 🔧 开始读取文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 10:57:44] [SessionUtils] 🔧 文件读取成功，内容长度: 618752
[2025-07-17 10:57:44] [SessionUtils] 🔧 消息历史数量: 66
[2025-07-17 10:57:44] [SessionUtils] 🔧 检查消息 65, kind: response
[2025-07-17 10:57:44] [SessionUtils] ✅ 找到最新回复，长度: 756
[2025-07-17 10:57:44] [SessionUtils] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-17 10:57:44] [SessionUtils] ❌ 异常类型: EInOutError
[2025-07-17 10:57:44] ❌ ExtractLatestResponseFromJSON 返回空结果
[2025-07-17 10:57:44] SendToAI 返回，回复长度: 0
