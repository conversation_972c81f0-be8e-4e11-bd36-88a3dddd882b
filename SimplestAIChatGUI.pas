unit SimplestAIChatGUI;

{$mode objfpc}{$H+}

{
  ===============================================================================
  最简单的GUI异步AI对话示例 - 一行代码搞定！
  ===============================================================================
  
  这个示例展示了如何在GUI程序中用最简单的方式使用异步AI对话
  
  特点：
    - 发送消息只需要一行代码
    - 自动处理所有异步逻辑
    - 自动界面状态管理
    - 完全不阻塞界面
  
  ===============================================================================
}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  AsyncAIChat;

type
  TSimplestAIChatForm = class(TForm)
    InputEdit: TEdit;
    SendButton: TButton;
    ChatMemo: TMemo;
    StatusLabel: TLabel;
    Panel1: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure SendButtonClick(Sender: TObject);
    procedure InputEditKeyPress(Sender: TObject; var Key: char);
  private
    // AI事件处理方法
    procedure OnAIResponse(const Response: string);
    procedure OnAIError(const ErrorMsg: string);
    procedure OnAIStatus(const Status: string);
    
    // 辅助方法
    procedure AddChatMessage(const Speaker, Message: string);
    procedure SetUIEnabled(AEnabled: Boolean);
  end;

var
  SimplestAIChatForm: TSimplestAIChatForm;

implementation

{$R *.lfm}

// ===== 窗体事件处理 =====

procedure TSimplestAIChatForm.FormCreate(Sender: TObject);
begin
  // 设置窗体
  Caption := '最简单的异步AI对话 - 一行代码版本';
  Width := 600;
  Height := 500;
  
  // 设置控件
  Panel1.Height := 50;
  Panel1.Align := alBottom;
  
  ChatMemo.Parent := Self;
  ChatMemo.Align := alClient;
  ChatMemo.ReadOnly := True;
  ChatMemo.ScrollBars := ssVertical;
  
  InputEdit.Parent := Panel1;
  InputEdit.Left := 10;
  InputEdit.Top := 15;
  InputEdit.Width := 400;
  InputEdit.Anchors := [akLeft, akTop, akRight];
  
  SendButton.Parent := Panel1;
  SendButton.Left := 420;
  SendButton.Top := 13;
  SendButton.Width := 80;
  SendButton.Caption := '发送';
  SendButton.Anchors := [akTop, akRight];
  
  StatusLabel.Parent := Panel1;
  StatusLabel.Left := 510;
  StatusLabel.Top := 17;
  StatusLabel.Caption := '就绪';
  StatusLabel.Anchors := [akTop, akRight];
  
  // 初始化界面
  ChatMemo.Clear;
  AddChatMessage('系统', '最简单的异步AI对话系统已启动');
  AddChatMessage('系统', '只需要一行代码：SendAIMessage(''你好'', @OnResponse, @OnError, @OnStatus)');
  AddChatMessage('系统', '输入消息开始对话...');
end;

procedure TSimplestAIChatForm.FormDestroy(Sender: TObject);
begin
  // 清理资源（可选，程序退出时会自动调用）
  StopAIChat;
end;

procedure TSimplestAIChatForm.SendButtonClick(Sender: TObject);
var
  Message: string;
begin
  Message := Trim(InputEdit.Text);
  if Message = '' then
    Exit;
    
  // 显示用户消息
  AddChatMessage('👤 用户', Message);
  
  // 禁用界面，防止重复发送
  SetUIEnabled(False);
  
  // ===== 这就是全部！一行代码搞定异步AI对话！ =====
  if SendAIMessage(Message, @OnAIResponse, @OnAIError, @OnAIStatus) then
  begin
    // 发送成功，清空输入框
    InputEdit.Clear;
  end
  else
  begin
    // 发送失败，重新启用界面
    SetUIEnabled(True);
  end;
end;

procedure TSimplestAIChatForm.InputEditKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // 回车键
  begin
    Key := #0; // 阻止默认处理
    SendButtonClick(nil);
  end;
end;

// ===== AI事件处理方法 =====

procedure TSimplestAIChatForm.OnAIResponse(const Response: string);
begin
  // 收到AI回复
  AddChatMessage('🤖 AI', Response);
  
  // 重新启用界面
  SetUIEnabled(True);
  
  // 设置焦点到输入框
  if InputEdit.CanFocus then
    InputEdit.SetFocus;
end;

procedure TSimplestAIChatForm.OnAIError(const ErrorMsg: string);
begin
  // 处理错误
  AddChatMessage('❌ 错误', ErrorMsg);
  
  // 重新启用界面
  SetUIEnabled(True);
end;

procedure TSimplestAIChatForm.OnAIStatus(const Status: string);
begin
  // 更新状态显示
  StatusLabel.Caption := Status;
  Application.ProcessMessages;
end;

// ===== 辅助方法 =====

procedure TSimplestAIChatForm.AddChatMessage(const Speaker, Message: string);
var
  TimeStr: string;
begin
  TimeStr := FormatDateTime('hh:nn:ss', Now);
  ChatMemo.Lines.Add(Format('[%s] %s: %s', [TimeStr, Speaker, Message]));
  ChatMemo.Lines.Add(''); // 空行分隔
  
  // 滚动到底部
  ChatMemo.SelStart := Length(ChatMemo.Text);
  ChatMemo.SelLength := 0;
end;

procedure TSimplestAIChatForm.SetUIEnabled(AEnabled: Boolean);
begin
  SendButton.Enabled := AEnabled;
  InputEdit.Enabled := AEnabled;
  
  if AEnabled then
    StatusLabel.Caption := '就绪'
  else
    StatusLabel.Caption := '处理中...';
end;

end.

{
  ===============================================================================
  使用总结
  ===============================================================================
  
  现在使用异步AI对话只需要：
  
  1. 引用单元：
     uses AsyncAIChat;
  
  2. 发送消息（一行代码）：
     SendAIMessage('你好', @OnResponse, @OnError, @OnStatus);
  
  3. 定义回调函数：
     procedure OnResponse(const Response: string);
     begin
       ShowMessage('AI: ' + Response);
     end;
  
  就这么简单！所有的异步逻辑、线程管理、状态控制都自动处理了！
  
  ===============================================================================
}
