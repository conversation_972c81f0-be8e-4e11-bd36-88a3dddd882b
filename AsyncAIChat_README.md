# AsyncAIChat - 异步AI对话通用组件

## 概述

AsyncAIChat 是一个基于 Free Pascal/Lazarus 的异步AI对话组件，专门解决GUI程序调用外部AI工具时的界面阻塞问题。

## 特性

✅ **完全异步** - 发送消息后立即返回，不阻塞界面  
✅ **实时监控** - 每2秒检查一次，快速检测AI回复  
✅ **无竞态条件** - 在进程启动前设置等待状态  
✅ **线程安全** - 使用Synchronize确保GUI操作安全  
✅ **易于集成** - 简单的API设计，几行代码即可使用  
✅ **错误处理** - 完善的异常处理机制  

## 核心原理

1. **守护线程监控**：持续监控会话文件的消息数量变化
2. **提前状态设置**：在启动AI进程前设置等待状态，避免竞态条件
3. **消息计数检测**：通过消息数量变化判断是否有新回复
4. **线程安全通信**：使用Synchronize在主线程中处理回复

## 文件结构

```
AsyncAIChat.pas                 - 核心组件单元
AsyncAIChatExample.pas          - GUI程序使用示例
AsyncAIChatConsoleExample.pas   - 控制台程序使用示例
AsyncAIChat_README.md          - 本文档
```

## 依赖

- **SessionUtils.pas** - 会话文件处理单元（需要单独提供）
- **Free Pascal 3.2+** 或 **Lazarus 2.0+**

## 快速开始

### 1. GUI程序中使用

```pascal
uses AsyncAIChat;

type
  TMyForm = class(TForm)
  private
    FAIChat: TAsyncAIChat;
    procedure OnAIResponse(const Response: string);
    procedure OnAIError(const ErrorMsg: string);
    procedure OnAIStatus(const Status: string);
  end;

procedure TMyForm.FormCreate(Sender: TObject);
begin
  // 创建组件
  FAIChat := TAsyncAIChat.Create;
  
  // 配置
  FAIChat.SetExecutable('C:\path\to\acli.exe');
  FAIChat.SetConfigFile('config.yml');
  FAIChat.SetSessionDir('sessions\session-id');
  
  // 设置事件
  FAIChat.OnResponse := @OnAIResponse;
  FAIChat.OnError := @OnAIError;
  FAIChat.OnStatus := @OnAIStatus;
  
  // 启动监控
  FAIChat.StartMonitoring;
end;

procedure TMyForm.SendMessage;
begin
  if FAIChat.SendMessage('Hello AI!') then
    ShowMessage('消息发送成功，等待回复...');
end;

procedure TMyForm.OnAIResponse(const Response: string);
begin
  ShowMessage('AI回复: ' + Response);
end;
```

### 2. 控制台程序中使用

```pascal
program MyConsoleApp;
uses AsyncAIChat;

var
  AIChat: TAsyncAIChat;

procedure OnResponse(const Response: string);
begin
  WriteLn('AI: ', Response);
end;

begin
  AIChat := TAsyncAIChat.Create;
  AIChat.OnResponse := @OnResponse;
  AIChat.SetExecutable('acli.exe');
  AIChat.StartMonitoring;
  
  AIChat.SendMessage('Hello!');
  
  // 等待回复...
  ReadLn;
  AIChat.Free;
end.
```

## API 参考

### TAsyncAIChat 类

#### 构造和析构
- `constructor Create` - 创建实例
- `destructor Destroy` - 销毁实例并清理资源

#### 配置方法
- `SetExecutable(path: string)` - 设置AI工具可执行文件路径
- `SetConfigFile(path: string)` - 设置配置文件路径
- `SetSessionDir(path: string)` - 设置会话目录路径

#### 核心方法
- `SendMessage(message: string): Boolean` - 发送消息给AI
- `StartMonitoring` - 启动会话监控
- `StopMonitoring` - 停止会话监控

#### 事件
- `OnResponse: TAIResponseEvent` - 收到AI回复时触发
- `OnError: TAIErrorEvent` - 发生错误时触发
- `OnStatus: TAIStatusEvent` - 状态更新时触发

#### 属性
- `SessionDir: string` - 当前会话目录（只读）
- `IsWaitingForReply: Boolean` - 是否正在等待回复（只读）

## 事件类型定义

```pascal
TAIResponseEvent = procedure(const Response: string) of object;
TAIErrorEvent = procedure(const ErrorMsg: string) of object;
TAIStatusEvent = procedure(const Status: string) of object;
```

## 使用注意事项

### 1. 线程安全
- 所有事件回调都在主线程中执行
- 可以安全地在事件中更新GUI控件

### 2. 资源管理
- 程序退出前必须调用 `StopMonitoring`
- 建议在窗体的 `OnDestroy` 事件中释放组件

### 3. 错误处理
- 监听 `OnError` 事件处理各种错误情况
- 发送失败时会自动重置等待状态

### 4. 状态管理
- 发送消息时建议禁用发送按钮
- 在 `OnResponse` 或 `OnError` 中重新启用界面

## 配置示例

### RovoDev 配置
```pascal
FAIChat.SetExecutable('C:\test\acli.exe');
FAIChat.SetConfigFile('custom_persistence_config.yml');
FAIChat.SetSessionDir('custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2');
```

### 其他AI工具
只需修改可执行文件路径和参数格式即可适配其他AI工具。

## 故障排除

### 常见问题

1. **消息发送失败**
   - 检查可执行文件路径是否正确
   - 确认会话目录存在且可访问
   - 查看 `OnError` 事件中的错误信息

2. **收不到回复**
   - 确认会话文件格式正确
   - 检查 SessionUtils 是否正常工作
   - 查看监控线程是否正常启动

3. **界面卡死**
   - 确认使用的是异步版本
   - 检查是否正确设置了事件回调

## 集成到现有项目

### 1. 添加单元引用
在你的项目中添加对 `AsyncAIChat` 单元的引用：

```pascal
uses
  // 其他单元...
  AsyncAIChat, SessionUtils;
```

### 2. 修改项目文件
如果使用 Lazarus，在项目的 `.lpi` 文件中添加单元：

```xml
<Units Count="3">
  <Unit0>
    <Filename Value="YourMainUnit.pas"/>
  </Unit0>
  <Unit1>
    <Filename Value="AsyncAIChat.pas"/>
  </Unit1>
  <Unit2>
    <Filename Value="SessionUtils.pas"/>
  </Unit2>
</Units>
```

### 3. 编译设置
确保编译器能找到所有依赖单元：

```bash
# 命令行编译
fpc -Fu"path/to/units" -Fu"path/to/SessionUtils" YourProgram.pas

# 或在 Lazarus 中设置 Search Path
Project -> Project Options -> Compiler Options -> Paths -> Other unit files
```

## 高级用法

### 自定义AI工具适配
```pascal
// 适配不同的AI工具，只需修改进程参数
procedure TAsyncAIChat.SendMessage(const Message: string): Boolean;
begin
  // 对于 ChatGPT CLI 工具
  Process.Parameters.Add('chat');
  Process.Parameters.Add('--model');
  Process.Parameters.Add('gpt-4');
  Process.Parameters.Add(Message);

  // 对于 Claude CLI 工具
  Process.Parameters.Add('conversation');
  Process.Parameters.Add('--prompt');
  Process.Parameters.Add(Message);
end;
```

### 批量消息处理
```pascal
// 发送多个消息并等待所有回复
procedure SendMultipleMessages(Messages: TStringList);
var
  i: Integer;
begin
  for i := 0 to Messages.Count - 1 do
  begin
    FAIChat.SendMessage(Messages[i]);
    // 等待当前消息完成再发送下一个
    while FAIChat.IsWaitingForReply do
    begin
      Application.ProcessMessages;
      Sleep(100);
    end;
  end;
end;
```

## 版本历史

- **v1.0** (2025-07-17) - 初始版本，基于成功的RovoDev集成经验

## 作者

AI Assistant (Augment Agent)

## 许可证

本组件基于实际项目需求开发，可自由使用和修改。
