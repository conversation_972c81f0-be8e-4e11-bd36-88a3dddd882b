unit AsyncAIChatExample;

{$mode objfpc}{$H+}
{$codepage utf8}

{
  ===============================================================================
  AsyncAIChat 使用示例
  ===============================================================================
  
  这个示例展示了如何在GUI程序中使用 AsyncAIChat 组件
  
  主要步骤：
    1. 创建 TAsyncAIChat 实例
    2. 设置配置参数
    3. 设置事件回调
    4. 启动监控
    5. 发送消息
  
  ===============================================================================
}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  AsyncAIChat;

type
  TExampleForm = class(TForm)
    InputEdit: TEdit;
    SendButton: TButton;
    ChatMemo: TMemo;
    StatusLabel: TLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure SendButtonClick(Sender: TObject);
  private
    FAIChat: TAsyncAIChat;
    
    // AI事件处理方法
    procedure OnAIResponse(const Response: string);
    procedure OnAIError(const ErrorMsg: string);
    procedure OnAIStatus(const Status: string);
    
    // 辅助方法
    procedure AddChatMessage(const Speaker, Message: string);
    procedure SetUIEnabled(Enabled: Boolean);
  end;

var
  ExampleForm: TExampleForm;

implementation

{$R *.lfm}

// ===== 窗体事件处理 =====

procedure TExampleForm.FormCreate(Sender: TObject);
begin
  // 创建异步AI对话组件
  FAIChat := TAsyncAIChat.Create;
  
  // 配置AI工具路径和参数
  FAIChat.SetExecutable('C:\test\acli.exe');
  FAIChat.SetConfigFile('custom_persistence_config.yml');
  FAIChat.SetSessionDir('custom_sessions\your-session-id-here');
  
  // 设置事件回调
  FAIChat.OnResponse := @OnAIResponse;
  FAIChat.OnError := @OnAIError;
  FAIChat.OnStatus := @OnAIStatus;
  
  // 启动监控
  FAIChat.StartMonitoring;
  
  // 初始化界面
  ChatMemo.Clear;
  StatusLabel.Caption := '就绪';
  AddChatMessage('系统', '异步AI对话系统已启动');
end;

procedure TExampleForm.FormDestroy(Sender: TObject);
begin
  // 清理资源
  if Assigned(FAIChat) then
  begin
    FAIChat.StopMonitoring;
    FAIChat.Free;
  end;
end;

procedure TExampleForm.SendButtonClick(Sender: TObject);
var
  Message: string;
begin
  Message := Trim(InputEdit.Text);
  if Message = '' then
    Exit;
    
  // 显示用户消息
  AddChatMessage('用户', Message);
  
  // 禁用界面，防止重复发送
  SetUIEnabled(False);
  
  // 发送消息给AI（异步）
  if FAIChat.SendMessage(Message) then
  begin
    // 发送成功，清空输入框
    InputEdit.Clear;
  end
  else
  begin
    // 发送失败，重新启用界面
    SetUIEnabled(True);
  end;
end;

// ===== AI事件处理方法 =====

procedure TExampleForm.OnAIResponse(const Response: string);
begin
  // 收到AI回复
  AddChatMessage('🤖 AI', Response);
  
  // 重新启用界面
  SetUIEnabled(True);
  
  // 设置焦点到输入框
  if InputEdit.CanFocus then
    InputEdit.SetFocus;
end;

procedure TExampleForm.OnAIError(const ErrorMsg: string);
begin
  // 处理错误
  AddChatMessage('❌ 错误', ErrorMsg);
  
  // 重新启用界面
  SetUIEnabled(True);
end;

procedure TExampleForm.OnAIStatus(const Status: string);
begin
  // 更新状态显示
  StatusLabel.Caption := Status;
  Application.ProcessMessages;
end;

// ===== 辅助方法 =====

procedure TExampleForm.AddChatMessage(const Speaker, Message: string);
var
  TimeStr: string;
begin
  TimeStr := FormatDateTime('hh:nn:ss', Now);
  ChatMemo.Lines.Add(Format('[%s] %s: %s', [TimeStr, Speaker, Message]));
  ChatMemo.Lines.Add(''); // 空行分隔
  
  // 滚动到底部
  ChatMemo.SelStart := Length(ChatMemo.Text);
  ChatMemo.SelLength := 0;
end;

procedure TExampleForm.SetUIEnabled(Enabled: Boolean);
begin
  SendButton.Enabled := Enabled;
  InputEdit.Enabled := Enabled;
  
  if Enabled then
    StatusLabel.Caption := '就绪'
  else
    StatusLabel.Caption := '处理中...';
end;

end.

{
  ===============================================================================
  使用说明
  ===============================================================================
  
  1. 配置设置：
     - SetExecutable: 设置AI工具的可执行文件路径
     - SetConfigFile: 设置配置文件路径（可选）
     - SetSessionDir: 设置会话目录路径
  
  2. 事件处理：
     - OnResponse: 收到AI回复时触发
     - OnError: 发生错误时触发
     - OnStatus: 状态更新时触发
  
  3. 发送消息：
     - 调用 SendMessage 方法发送消息
     - 方法立即返回，不阻塞界面
     - 回复通过 OnResponse 事件异步返回
  
  4. 生命周期：
     - FormCreate: 创建组件并启动监控
     - FormDestroy: 停止监控并释放资源
  
  5. 界面管理：
     - 发送消息时禁用界面，防止重复发送
     - 收到回复或错误时重新启用界面
     - 实时更新状态显示
  
  ===============================================================================
}
