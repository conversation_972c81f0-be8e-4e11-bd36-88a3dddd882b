program TestSessionExtraction;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils, SessionUtils;

var
  SessionDir: string;
  Response: string;

begin
  WriteLn('=== 测试会话提取功能 ===');
  
  SessionDir := 'custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2';
  WriteLn('会话目录: ', SessionDir);
  
  if DirectoryExists(SessionDir) then
    WriteLn('✅ 会话目录存在')
  else
  begin
    WriteLn('❌ 会话目录不存在');
    Exit;
  end;
  
  if FileExists(SessionDir + '\session_context.json') then
    WriteLn('✅ 会话文件存在')
  else
  begin
    WriteLn('❌ 会话文件不存在');
    Exit;
  end;
  
  WriteLn('');
  WriteLn('=== 开始提取最新回复 ===');
  
  try
    Response := ProcessSessionResponse(SessionDir, True);
    
    if Response <> '' then
    begin
      WriteLn('✅ 成功提取回复！');
      WriteLn('回复长度: ', Length(Response), ' 字符');
      WriteLn('');
      WriteLn('=== 回复内容 ===');
      WriteLn(Response);
      WriteLn('=== 回复结束 ===');
    end
    else
    begin
      WriteLn('❌ 提取回复失败');
    end;
    
  except
    on E: Exception do
    begin
      WriteLn('❌ 提取过程出错: ', E.Message);
      WriteLn('错误类型: ', E.ClassName);
    end;
  end;
  
  WriteLn('');
  WriteLn('=== 测试完成 ===');
  WriteLn('按回车键退出...');
  ReadLn;
end.
