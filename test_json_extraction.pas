program TestJsonExtraction;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils, SessionUtils;

var
  JsonFile: string;
  Response: string;

begin
  WriteLn('=== 测试 JSON 提取功能 ===');
  
  JsonFile := 'custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json';
  WriteLn('JSON 文件: ', JsonFile);
  
  if FileExists(JsonFile) then
    WriteLn('✅ JSON 文件存在')
  else
  begin
    WriteLn('❌ JSON 文件不存在');
    Exit;
  end;
  
  WriteLn('');
  WriteLn('=== 开始提取最新回复 ===');
  
  try
    Response := ExtractLatestResponseFromJSON(JsonFile);
    
    if Response <> '' then
    begin
      WriteLn('✅ 成功提取回复！');
      WriteLn('回复长度: ', Length(Response), ' 字符');
      WriteLn('');
      WriteLn('=== 回复内容前100字符 ===');
      WriteLn(Copy(Response, 1, 100));
      WriteLn('=== 回复内容结束 ===');
    end
    else
    begin
      WriteLn('❌ 提取回复失败 - 返回空字符串');
    end;
    
  except
    on E: Exception do
    begin
      WriteLn('❌ 提取过程出错: ', E.Message);
      WriteLn('错误类型: ', E.ClassName);
    end;
  end;
  
  WriteLn('');
  WriteLn('=== 测试完成 ===');
  WriteLn('按回车键退出...');
  ReadLn;
end.
