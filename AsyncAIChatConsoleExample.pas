program AsyncAIChatConsoleExample;

{$mode objfpc}{$H+}
{$codepage utf8}

{
  ===============================================================================
  AsyncAIChat 控制台使用示例
  ===============================================================================
  
  这个示例展示了如何在控制台程序中使用 AsyncAIChat 组件
  
  特点：
    - 简单的命令行界面
    - 异步处理，不阻塞用户输入
    - 完整的错误处理
  
  ===============================================================================
}

uses
  Classes, SysUtils, AsyncAIChat;

type
  TConsoleAIChat = class
  private
    FAIChat: TAsyncAIChat;
    FWaitingForResponse: Boolean;
    
    // AI事件处理方法
    procedure OnAIResponse(const Response: string);
    procedure OnAIError(const ErrorMsg: string);
    procedure OnAIStatus(const Status: string);
    
  public
    constructor Create;
    destructor Destroy; override;
    
    procedure Initialize;
    procedure SendMessage(const Message: string);
    procedure WaitForCompletion;
    
    property WaitingForResponse: Boolean read FWaitingForResponse;
  end;

// ===== TConsoleAIChat 实现 =====

constructor TConsoleAIChat.Create;
begin
  inherited Create;
  FAIChat := TAsyncAIChat.Create;
  FWaitingForResponse := False;
  
  // 设置事件回调
  FAIChat.OnResponse := @OnAIResponse;
  FAIChat.OnError := @OnAIError;
  FAIChat.OnStatus := @OnAIStatus;
end;

destructor TConsoleAIChat.Destroy;
begin
  if Assigned(FAIChat) then
  begin
    FAIChat.StopMonitoring;
    FAIChat.Free;
  end;
  inherited Destroy;
end;

procedure TConsoleAIChat.Initialize;
begin
  WriteLn('=== 异步AI对话控制台示例 ===');
  WriteLn('');
  
  // 配置AI工具（使用实际的路径）
  FAIChat.SetExecutable('C:\test\acli.exe');
  FAIChat.SetConfigFile('custom_persistence_config.yml');
  FAIChat.SetSessionDir('custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2');
  
  // 启动监控
  FAIChat.StartMonitoring;
  
  WriteLn('AI对话系统已初始化');
  WriteLn('输入消息开始对话，输入 "quit" 退出');
  WriteLn('');
end;

procedure TConsoleAIChat.SendMessage(const Message: string);
begin
  if FWaitingForResponse then
  begin
    WriteLn('正在等待上一个回复，请稍候...');
    Exit;
  end;
  
  WriteLn('[发送] ' + Message);
  FWaitingForResponse := True;
  
  if not FAIChat.SendMessage(Message) then
  begin
    FWaitingForResponse := False;
  end;
end;

procedure TConsoleAIChat.WaitForCompletion;
begin
  while FWaitingForResponse do
  begin
    Sleep(100);
  end;
end;

procedure TConsoleAIChat.OnAIResponse(const Response: string);
begin
  WriteLn('');
  WriteLn('[AI回复] ' + Response);
  WriteLn('');
  FWaitingForResponse := False;
end;

procedure TConsoleAIChat.OnAIError(const ErrorMsg: string);
begin
  WriteLn('');
  WriteLn('[错误] ' + ErrorMsg);
  WriteLn('');
  FWaitingForResponse := False;
end;

procedure TConsoleAIChat.OnAIStatus(const Status: string);
begin
  WriteLn('[状态] ' + Status);
end;

// ===== 主程序 =====

var
  ChatApp: TConsoleAIChat;
  UserInput: string;

begin
  ChatApp := TConsoleAIChat.Create;
  try
    ChatApp.Initialize;
    
    repeat
      Write('> ');
      ReadLn(UserInput);
      UserInput := Trim(UserInput);
      
      if UserInput = '' then
        Continue;
        
      if LowerCase(UserInput) = 'quit' then
        Break;
        
      ChatApp.SendMessage(UserInput);
      
      // 可选：等待回复完成再接受下一个输入
      // ChatApp.WaitForCompletion;
      
    until False;
    
    WriteLn('正在退出...');
    
  finally
    ChatApp.Free;
  end;
end.

{
  ===============================================================================
  编译和运行说明
  ===============================================================================
  
  1. 编译：
     fpc -Fu"path/to/SessionUtils" AsyncAIChatConsoleExample.pas
  
  2. 运行前配置：
     - 修改 SetExecutable 中的AI工具路径
     - 修改 SetSessionDir 中的会话目录路径
     - 确保配置文件存在
  
  3. 使用：
     - 运行程序
     - 输入消息并按回车
     - 等待AI回复
     - 输入 "quit" 退出
  
  4. 特点：
     - 异步处理，可以在等待回复时继续输入
     - 完整的状态显示
     - 简洁的错误处理
  
  ===============================================================================
}
