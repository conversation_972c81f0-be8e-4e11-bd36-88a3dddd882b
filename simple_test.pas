program SimpleTest;

{$mode objfpc}{$H+}
{$codepage utf8}

uses
  SysUtils, Classes, Forms, Controls, Graphics, Dialogs, StdCtrls;

type
  TSimpleForm = class(TForm)
  private
    Memo1: TMemo;
  public
    constructor Create(AOwner: TComponent); override;
  end;

constructor TSimpleForm.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  
  Caption := '简单测试窗体';
  Width := 400;
  Height := 300;
  Position := poScreenCenter;
  
  Memo1 := TMemo.Create(Self);
  Memo1.Parent := Self;
  Memo1.Align := alClient;
  Memo1.Lines.Add('程序启动成功！');
  Memo1.Lines.Add('当前时间: ' + FormatDateTime('yyyy-mm-dd hh:nn:ss', Now));
  Memo1.Lines.Add('当前目录: ' + GetCurrentDir);
  
  if FileExists('custom_persistence_config.yml') then
    Memo1.Lines.Add('✅ 配置文件存在')
  else
    Memo1.Lines.Add('❌ 配置文件不存在');
    
  if FileExists('C:\test\acli.exe') then
    Memo1.Lines.Add('✅ acli.exe 存在')
  else
    Memo1.Lines.Add('❌ acli.exe 不存在');
end;

var
  Application: TApplication;
  SimpleForm: TSimpleForm;

begin
  Application := TApplication.Create(nil);
  try
    Application.Initialize;
    SimpleForm := TSimpleForm.Create(Application);
    Application.MainForm := SimpleForm;
    SimpleForm.Show;
    Application.Run;
  finally
    Application.Free;
  end;
end.
