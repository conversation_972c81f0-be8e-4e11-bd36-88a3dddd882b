<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectSession>
    <PathDelim Value="\"/>
    <Version Value="12"/>
    <BuildModes Active="Default"/>
    <Units>
      <Unit>
        <Filename Value="testproject1.lpr"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="-1"/>
        <WindowIndex Value="-1"/>
        <TopLine Value="-1"/>
        <CursorPos X="-1" Y="-1"/>
        <UsageCount Value="22"/>
      </Unit>
      <Unit>
        <Filename Value="testunit1.pas"/>
        <IsPartOfProject Value="True"/>
        <ComponentName Value="Form1"/>
        <HasResources Value="True"/>
        <ResourceBaseClass Value="Form"/>
        <IsVisibleTab Value="True"/>
        <TopLine Value="461"/>
        <CursorPos X="64" Y="461"/>
        <UsageCount Value="22"/>
        <Loaded Value="True"/>
        <LoadedDesigner Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="SessionUtils.pas"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="3"/>
        <UsageCount Value="21"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="SessionReader.pas"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="483"/>
        <CursorPos X="27" Y="508"/>
        <UsageCount Value="10"/>
      </Unit>
      <Unit>
        <Filename Value="C:\lazarus\fpc\3.2.2\source\packages\fcl-process\src\processbody.inc"/>
        <EditorIndex Value="1"/>
        <CursorPos X="20" Y="45"/>
        <UsageCount Value="10"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="custom_persistence_config.yml"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="-1"/>
        <WindowIndex Value="-1"/>
        <TopLine Value="-1"/>
        <CursorPos X="-1" Y="-1"/>
        <UsageCount Value="21"/>
        <DefaultSyntaxHighlighter Value="None"/>
      </Unit>
      <Unit>
        <Filename Value="C:\lazarus\fpc\3.2.2\source\packages\fcl-process\src\win\process.inc"/>
        <EditorIndex Value="2"/>
        <TopLine Value="229"/>
        <CursorPos X="3" Y="244"/>
        <UsageCount Value="10"/>
        <Loaded Value="True"/>
      </Unit>
    </Units>
    <JumpHistory HistoryIndex="29">
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="165" Column="42"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="10" Column="19"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="10" Column="33" TopLine="10"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="235" Column="59" TopLine="213"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="236" Column="31" TopLine="213"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="295" Column="47" TopLine="269"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="448" Column="47" TopLine="422"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="797" Column="43" TopLine="772"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="205" Column="62" TopLine="201"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="83" Column="32" TopLine="100"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="37" Column="14" TopLine="30"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="10" Column="10"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="274" Column="23" TopLine="270"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="290" Column="23" TopLine="275"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="291" Column="32" TopLine="291"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="305" Column="21" TopLine="295"/>
      </Position>
      <Position>
        <Filename Value="C:\lazarus\fpc\3.2.2\source\packages\fcl-process\src\processbody.inc"/>
        <Caret Line="113" Column="15" TopLine="104"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="312" Column="46" TopLine="307"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="319" Column="20" TopLine="303"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="322" Column="19" TopLine="313"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="329" Column="31" TopLine="320"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="334" Column="27" TopLine="325"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="337" Column="23" TopLine="327"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="404" Column="17" TopLine="276"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="274" Column="23" TopLine="269"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="307" Column="57" TopLine="294"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="470" Column="9" TopLine="460"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="308" Column="25" TopLine="299"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="470" Column="24" TopLine="455"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="308" Column="25" TopLine="299"/>
      </Position>
    </JumpHistory>
    <RunParams>
      <FormatVersion Value="2"/>
      <Modes ActiveMode=""/>
    </RunParams>
  </ProjectSession>
</CONFIG>
