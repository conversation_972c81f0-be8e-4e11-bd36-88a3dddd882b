program TestSimpleAsyncAI;

{$mode objfpc}{$H+}

{
  ===============================================================================
  最简单的异步AI对话测试 - 真正的一行代码版本！
  ===============================================================================
  
  这个示例展示了如何用最简单的方式使用异步AI对话
  
  特点：
    - 真正的一行代码发送消息
    - 自动处理所有复杂逻辑
    - 简单的回调机制
  
  ===============================================================================
}

uses
  Classes, SysUtils, SimpleAsyncAIChat;

// 回调函数示例
procedure OnAIResponse(const Response: string);
begin
  WriteLn('');
  WriteLn('🤖 ', Response);
  WriteLn('');
  WriteLn('对话完成！输入新消息继续，或输入 "quit" 退出');
  Write('> ');
end;

procedure OnAIError(const ErrorMsg: string);
begin
  WriteLn('');
  WriteLn('❌ 错误: ', ErrorMsg);
  WriteLn('');
  Write('> ');
end;

procedure OnAIStatus(const Status: string);
begin
  WriteLn('[状态] ', Status);
end;

var
  UserInput: string;

begin
  WriteLn('===============================================================================');
  WriteLn('                    最简单异步AI对话 - 真正的一行代码版本');
  WriteLn('===============================================================================');
  WriteLn('');
  WriteLn('使用说明：');
  WriteLn('  - 输入消息并按回车发送给AI');
  WriteLn('  - 只需要一行代码：SendAIMessage(''你好'', @OnResponse, @OnError, @OnStatus)');
  WriteLn('  - 输入 "quit" 退出程序');
  WriteLn('');
  WriteLn('开始对话：');
  
  repeat
    Write('> ');
    ReadLn(UserInput);
    UserInput := Trim(UserInput);
    
    if UserInput = '' then
      Continue;
      
    if LowerCase(UserInput) = 'quit' then
      Break;
    
    // ===== 这就是全部！真正的一行代码搞定异步AI对话！ =====
    SendAIMessage(UserInput, @OnAIResponse, @OnAIError, @OnAIStatus);
    
  until False;
  
  WriteLn('');
  WriteLn('再见！');
end.

{
  ===============================================================================
  使用说明
  ===============================================================================
  
  现在使用异步AI对话真的只需要一行代码：
  
  SendAIMessage('你好', @OnResponse, @OnError, @OnStatus);
  
  就这么简单！所有的复杂逻辑都被封装了：
  - 进程创建和管理
  - 超时处理
  - 错误处理
  - 状态更新
  
  你只需要：
  1. 引用 SimpleAsyncAIChat 单元
  2. 定义回调函数
  3. 调用 SendAIMessage
  
  完成！
  
  ===============================================================================
}
