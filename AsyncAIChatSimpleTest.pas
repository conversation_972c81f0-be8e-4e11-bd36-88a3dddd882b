program AsyncAIChatSimpleTest;

{$mode objfpc}{$H+}

uses
  Classes, SysUtils, AsyncAIChat;

type
  TSimpleTest = class
  private
    FAIChat: TAsyncAIChat;
    FResponseReceived: Boolean;
    
    procedure OnAIResponse(const Response: string);
    procedure OnAIError(const ErrorMsg: string);
    procedure OnAIStatus(const Status: string);
    
  public
    constructor Create;
    destructor Destroy; override;
    
    procedure RunTest;
    property ResponseReceived: <PERSON><PERSON><PERSON> read FResponseReceived;
  end;

constructor TSimpleTest.Create;
begin
  inherited Create;
  FAIChat := TAsyncAIChat.Create;
  FResponseReceived := False;
  
  // 设置事件回调
  FAIChat.OnResponse := @OnAIResponse;
  FAIChat.OnError := @OnAIError;
  FAIChat.OnStatus := @OnAIStatus;
end;

destructor TSimpleTest.Destroy;
begin
  if Assigned(FAIChat) then
  begin
    FAIChat.StopMonitoring;
    FAIChat.Free;
  end;
  inherited Destroy;
end;

procedure TSimpleTest.OnAIResponse(const Response: string);
begin
  WriteLn('');
  WriteLn('✅ 收到回复: ', Response);
  WriteLn('');
  FResponseReceived := True;
end;

procedure TSimpleTest.OnAIError(const ErrorMsg: string);
begin
  WriteLn('');
  WriteLn('❌ 错误: ', ErrorMsg);
  WriteLn('');
  FResponseReceived := True; // 也算完成了
end;

procedure TSimpleTest.OnAIStatus(const Status: string);
begin
  WriteLn('[状态] ', Status);
end;

procedure TSimpleTest.RunTest;
begin
  WriteLn('=== AsyncAIChat 组件简单测试 ===');
  WriteLn('');
  
  // 配置AI工具
  FAIChat.SetExecutable('C:\test\acli.exe');
  FAIChat.SetConfigFile('custom_persistence_config.yml');
  FAIChat.SetSessionDir('custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2');
  
  // 启动监控
  FAIChat.StartMonitoring;
  
  WriteLn('发送测试消息...');
  if FAIChat.SendMessage('简单测试消息') then
  begin
    WriteLn('消息发送成功，等待回复...');
    
    // 等待回复或错误
    while not FResponseReceived do
    begin
      Sleep(100);
    end;
  end
  else
  begin
    WriteLn('消息发送失败');
  end;
  
  WriteLn('测试完成');
end;

var
  Test: TSimpleTest;

begin
  Test := TSimpleTest.Create;
  try
    Test.RunTest;
  finally
    Test.Free;
  end;
  
  WriteLn('按回车键退出...');
  ReadLn;
end.
