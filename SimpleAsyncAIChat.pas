unit SimpleAsyncAIChat;

{$mode objfpc}{$H+}

{
  ===============================================================================
  SimpleAsyncAIChat - 简化版异步AI对话组件
  ===============================================================================
  
  这是一个简化版本，专注于核心功能：
  - 单函数调用接口
  - 异步消息发送
  - 简单的回调机制
  
  使用方法：
    SendAIMessage('你好', @OnResponse, @OnError);
  
  ===============================================================================
}

interface

uses
  Classes, SysUtils, Process;

type
  // 事件类型定义 - 支持普通过程和对象方法
  TAIResponseEvent = procedure(const Response: string);
  TAIErrorEvent = procedure(const ErrorMsg: string);
  TAIStatusEvent = procedure(const Status: string);

// ===== 超简单的单函数调用接口 =====

{
  SendAIMessage - 一行代码搞定异步AI对话！
  
  参数：
    Message: 要发送给AI的消息
    OnResponse: 收到回复时的回调函数（可选）
    OnError: 发生错误时的回调函数（可选）
    OnStatus: 状态更新时的回调函数（可选）
  
  返回值：
    True: 消息发送成功
    False: 消息发送失败
  
  使用示例：
    SendAIMessage('你好', @MyResponseHandler, @MyErrorHandler);
}
function SendAIMessage(
  const Message: string;
  OnResponse: TAIResponseEvent = nil;
  OnError: TAIErrorEvent = nil;
  OnStatus: TAIStatusEvent = nil
): Boolean;

implementation

// ===== 全局变量 =====
var
  CurrentOnResponse: TAIResponseEvent = nil;
  CurrentOnError: TAIErrorEvent = nil;
  CurrentOnStatus: TAIStatusEvent = nil;

// ===== 辅助函数 =====

procedure DoStatus(const Status: string);
begin
  if Assigned(CurrentOnStatus) then
    CurrentOnStatus(Status);
end;

procedure DoError(const ErrorMsg: string);
begin
  if Assigned(CurrentOnError) then
    CurrentOnError(ErrorMsg);
end;

procedure DoResponse(const Response: string);
begin
  if Assigned(CurrentOnResponse) then
    CurrentOnResponse(Response);
end;

// ===== 主要函数实现 =====

function SendAIMessage(
  const Message: string;
  OnResponse: TAIResponseEvent = nil;
  OnError: TAIErrorEvent = nil;
  OnStatus: TAIStatusEvent = nil
): Boolean;
var
  Process: TProcess;
  WaitCount: Integer;
  ExitCode: Integer;
begin
  Result := False;
  
  // 设置回调
  CurrentOnResponse := OnResponse;
  CurrentOnError := OnError;
  CurrentOnStatus := OnStatus;
  
  try
    DoStatus('准备发送消息...');
    
    // 创建并执行AI进程
    Process := TProcess.Create(nil);
    try
      Process.Executable := 'C:\test\acli.exe';
      Process.Parameters.Add('rovodev');
      Process.Parameters.Add('run');
      Process.Parameters.Add('--config-file');
      Process.Parameters.Add('custom_persistence_config.yml');
      Process.Parameters.Add(Message);
      
      Process.ShowWindow := swoHide;
      Process.Options := [poNoConsole];
      
      DoStatus('正在发送消息到AI...');
      Process.Execute;
      
      // 等待进程完成
      WaitCount := 0;
      while Process.Running and (WaitCount < 1200) do  // 120秒
      begin
        Sleep(100);
        Inc(WaitCount);
        if WaitCount mod 50 = 0 then
          DoStatus('AI处理中... ' + IntToStr(WaitCount div 10) + '秒');
      end;
      
      if Process.Running then
      begin
        DoStatus('AI进程超时，正在终止...');
        Process.Terminate(1);
        ExitCode := -2;
      end
      else
      begin
        ExitCode := Process.ExitStatus;
        DoStatus('AI进程完成，退出代码: ' + IntToStr(ExitCode));
      end;
      
    finally
      Process.Free;
    end;
    
    if ExitCode = 0 then
    begin
      DoStatus('消息发送成功');
      DoResponse('AI回复：消息已处理（简化版本暂不支持回复提取）');
      Result := True;
    end
    else
    begin
      DoError('消息发送失败，退出代码: ' + IntToStr(ExitCode));
    end;
    
  except
    on E: Exception do
    begin
      DoError('发送消息时出错: ' + E.Message);
    end;
  end;
end;

end.
